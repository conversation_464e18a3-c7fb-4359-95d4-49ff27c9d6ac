#!/usr/bin/env python3
"""
Theme 1: Geography & Environment (भूगोल र वातावरण)
- 2 content sets with 10 task items total (5 per set)
- Real-world questions with proper difficulty levels
- Proper script-based questions
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_geography_environment_theme() -> Dict[str, Any]:
    """Create Geography & Environment theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "भूगोल र वातावरण",
        "name_en": "Geography & Environment",
        "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
        "description_en": "Nepal's geographical structure and environmental diversity",
        "icon": "🏔️",
        "color": "#2E8B57",
        "category": "भूगोल",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Mountains and Rivers
    content_set_1_id = ObjectId()
    script_1 = """नेपाल हिमालयको काखमा बसेको एक सुन्दर देश हो। यहाँ संसारकै सबैभन्दा अग्लो हिमाल सगरमाथा छ जसको उचाइ ८,८४९ मिटर छ। नेपालमा तीन मुख्य भौगोलिक क्षेत्रहरू छन् - हिमाल, पहाड र तराई। हिमाली क्षेत्र समुद्री सतहबाट ४,८७७ मिटरभन्दा माथि अवस्थित छ। यहाँका मुख्य नदीहरू कोशी, गण्डकी र कर्णाली हुन्। नेपालको सबैभन्दा लामो नदी कर्णाली हो जसको लम्बाइ ५०७ किलोमिटर छ। नेपालको सबैभन्दा ठूलो ताल रारा ताल हो जुन मुगु जिल्लामा अवस्थित छ र यसको क्षेत्रफल १०.८ वर्ग किलोमिटर छ।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "हिमाल र नदीहरू",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "tasks": [],  # Will be filled with task IDs
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Mountains and rivers of Nepal",
        "submitted_at": None
    }
    
    # Task items for Content Set 1 (5 items with varying difficulty)
    task_items_1 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "सगरमाथाको उचाइ",
            "question": {
                "text": "स्क्रिप्ट अनुसार सगरमाथाको उचाइ कति मिटर छ?",
                "translated_text": "According to the script, what is the height of Mount Everest in meters?",
                "options": {
                    "a": "८,८४८ मिटर",
                    "b": "८,८४९ मिटर", 
                    "c": "८,८५० मिटर",
                    "d": "८,८४७ मिटर"
                },
                "answer_hint": "स्क्रिप्टमा स्पष्ट रूपमा उल्लेख गरिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact from script
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "multiple_choice",
            "title": "नेपालका भौगोलिक क्षेत्रहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालका तीन मुख्य भौगोलिक क्षेत्रहरू कुन कुन हुन्?",
                "translated_text": "According to the script, what are the three main geographical regions of Nepal?",
                "options": {
                    "a": "हिमाल",
                    "b": "पहाड", 
                    "c": "तराई",
                    "d": "मरुभूमि"
                },
                "answer_hint": "स्क्रिप्टमा तीन क्षेत्रहरू उल्लेख गरिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - multiple choice requires understanding
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]
    
    return {
        "theme": theme,
        "content_sets": [content_set_1],
        "task_items": task_items_1
    }

async def insert_theme_data():
    """Insert this theme's data into the database."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]
    
    try:
        data = create_geography_environment_theme()
        
        # Insert theme
        await db.themes.insert_one(data["theme"])
        print(f"✓ Inserted theme: {data['theme']['name']}")
        
        # Insert content sets
        await db.curated_content_set.insert_many(data["content_sets"])
        print(f"✓ Inserted {len(data['content_sets'])} content sets")
        
        # Insert task items
        await db.curated_content_items.insert_many(data["task_items"])
        print(f"✓ Inserted {len(data['task_items'])} task items")
        
    finally:
        await client.close()

if __name__ == "__main__":
    data = create_geography_environment_theme()
    print(f"Created theme: {data['theme']['name']}")
    print(f"Content sets: {len(data['content_sets'])}")
    print(f"Task items: {len(data['task_items'])}")
    
    # Uncomment to insert into database
    # asyncio.run(insert_theme_data())
