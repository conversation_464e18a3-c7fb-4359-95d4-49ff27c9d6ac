#!/usr/bin/env python3
"""
Fix Theme 3 by adding missing tasks directly to the database
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

async def fix_theme_3():
    """Add missing tasks to Theme 3."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]
    
    try:
        current_time = datetime.now(timezone.utc)
        
        # Get Theme 3 content sets
        theme_3 = await db.themes.find_one({"name": "इतिहास र विरासत"})
        if not theme_3:
            print("❌ Theme 3 not found")
            return
        
        theme_id = theme_3["_id"]
        content_sets = await db.curated_content_sets.find({"theme_id": theme_id}).to_list(None)
        
        if len(content_sets) != 2:
            print(f"❌ Expected 2 content sets, found {len(content_sets)}")
            return
        
        # Add missing tasks to each content set
        for i, content_set in enumerate(content_sets, 1):
            content_set_id = content_set["_id"]
            existing_tasks = await db.tasks.find({"task_set_id": content_set_id}).to_list(None)
            existing_count = len(existing_tasks)
            
            print(f"📋 Content Set {i}: {existing_count} existing tasks")
            
            if existing_count >= 5:
                print(f"✅ Content Set {i} already has enough tasks")
                continue
            
            # Create missing tasks
            missing_count = 5 - existing_count
            new_tasks = []
            
            for j in range(missing_count):
                task_num = existing_count + j + 1
                
                if i == 1:  # Content Set 1: Ancient Dynasties
                    questions = [
                        ("लिच्छवि काल कहिलेदेखि कहिलेसम्म थियो?", {"a": "३०० - ८७९ ईस्वी", "b": "२०० - ७०० ईस्वी", "c": "४०० - ९०० ईस्वी", "d": "५०० - १००० ईस्वी"}, "a", "प्राचीन नेपालको स्वर्ण युग"),
                        ("मल्ल काल कुन शताब्दीमा थियो?", {"a": "१० औं शताब्दी", "b": "१२ औं शताब्दी", "c": "१४ औं शताब्दी", "d": "१६ औं शताब्दी"}, "c", "मध्यकालीन नेपाल"),
                        ("किरात राजवंशको राजधानी कहाँ थियो?", {"a": "काठमाडौं", "b": "पाटन", "c": "भक्तपुर", "d": "पाटन र भक्तपुर"}, "a", "प्राचीन काठमाडौं उपत्यका")
                    ]
                else:  # Content Set 2: Modern History
                    questions = [
                        ("राणा शासन कहिले सुरु भयो?", {"a": "१८४६ ईस्वी", "b": "१८५० ईस्वी", "c": "१८६० ईस्वी", "d": "१८७० ईस्वी"}, "a", "जंगबहादुर राणाको समय"),
                        ("नेपालमा लोकतन्त्र कहिले स्थापना भयो?", {"a": "१९५० ईस्वी", "b": "१९५१ ईस्वी", "c": "१९५९ ईस्वी", "d": "१९६२ ईस्वी"}, "b", "राणा शासनको अन्त्य"),
                        ("नेपालको संविधान कहिले जारी भयो?", {"a": "२०७२ साल", "b": "२०७३ साल", "c": "२०७४ साल", "d": "२०७५ साल"}, "a", "नयाँ संविधान")
                    ]
                
                if j < len(questions):
                    question, options, answer, hint = questions[j]
                    
                    task = {
                        "_id": ObjectId(),
                        "task_set_id": content_set_id,
                        "user_id": USER_ID,
                        "session_id": str(uuid.uuid4()),
                        "question": {
                            "text": question,
                            "translated_text": f"Question {task_num} in English",
                            "options": options,
                            "answer_hint": hint,
                            "difficulty": 2,
                            "title": f"प्रश्न {task_num}"
                        },
                        "correct_answer": {
                            "value": answer,
                            "type": "single_choice"
                        },
                        "created_at": current_time,
                        "updated_at": current_time
                    }
                    new_tasks.append(task)
            
            if new_tasks:
                # Insert new tasks
                await db.tasks.insert_many(new_tasks)
                print(f"✅ Added {len(new_tasks)} tasks to Content Set {i}")
                
                # Update content set task references
                all_tasks = await db.tasks.find({"task_set_id": content_set_id}).to_list(None)
                task_ids = [task["_id"] for task in all_tasks]
                
                await db.curated_content_sets.update_one(
                    {"_id": content_set_id},
                    {"$set": {"tasks": task_ids}}
                )
                print(f"✅ Updated Content Set {i} task references")
        
        print(f"\n🎉 Theme 3 fixed successfully!")
        
    except Exception as e:
        print(f"❌ Error fixing Theme 3: {e}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(fix_theme_3())
