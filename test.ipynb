from pymongo import AsyncMongoClient

from bson import ObjectId
object_ids = [
    ObjectId("683fd0c612e41f195c7a0750"),
    ObjectId("683fd0c612e41f195c7a0751"),
    ObjectId("683fd0c612e41f195c7a0752"),
]

pipeline = [
    {"$match": {"_id": {"$in": object_ids}}},
    {"$project": {
        "question": 1,
        "correct_answer": 1
    }}
]



result_list = await result.to_list(length=None)

result_list 

tasks = ""
for task in result_list:
        task["question"].pop("media_url", None)
        task["question"].pop("metadata", None)
        tasks += f"Question: {task['question']}\nAnswer: {task['correct_answer']}\n\n"


tasks

from pymongo import AsyncMongoClient
client = AsyncMongoClient("mongodb+srv://diwas:<EMAIL>/")["test_nepali_app"]
result =await client["curated_content_set"].find().to_list(length=None)

result

result =await client["curated_content_items"].find().to_list(length=None)

result

for item in result:
    task_set=await client["curated_content_set"].find_one({"tasks":item["_id"]})
    if not task_set:
        await client["curated_content_items"].delete_one({"_id":item["_id"]})

