#!/usr/bin/env python3
"""
Add missing 'type' field at task level for all theme files.
The type field should be both at task level AND in correct_answer.
"""

import os
import re

def add_task_level_type(file_path):
    """Add missing type field at task level."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to find task items missing the type field at task level
        # Look for: session_id followed directly by question (no type field)
        pattern = r'(\s+"session_id": str\(uuid\.uuid4\(\)\),\s+)("question": \{)'
        
        # Find all matches to count them
        matches = list(re.finditer(pattern, content))
        
        if not matches:
            print(f"✅ {os.path.basename(file_path)}: Already has task-level types")
            return
        
        # For each match, we need to determine the correct type
        # We'll look ahead to find the correct_answer type
        def replace_with_type(match):
            session_id_part = match.group(1)
            question_part = match.group(2)
            
            # Look for the correct_answer type in the content after this match
            remaining_content = content[match.end():]
            
            # Find the next correct_answer section
            type_match = re.search(r'"correct_answer": \{\s*"value": [^}]+,\s*"type": "([^"]+)"', remaining_content)
            
            if type_match:
                task_type = type_match.group(1)
                return f'{session_id_part}"type": "{task_type}",\n        {question_part}'
            else:
                # Default to single_choice if we can't find it
                return f'{session_id_part}"type": "single_choice",\n        {question_part}'
        
        # Apply the replacement
        new_content = re.sub(pattern, replace_with_type, content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"🔧 {os.path.basename(file_path)}: Added {len(matches)} task-level type fields")
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")

def main():
    """Add task-level types to all theme files."""
    content_dir = "content_generation"  # Look in content_generation directory
    
    # Find all theme files
    theme_files = []
    for filename in os.listdir(content_dir):
        if filename.startswith("theme_") and filename.endswith(".py"):
            theme_files.append(os.path.join(content_dir, filename))
    
    theme_files.sort()
    
    print(f"🚀 Processing {len(theme_files)} theme files to add task-level types...")
    print()
    
    for file_path in theme_files:
        add_task_level_type(file_path)
    
    print()
    print("🎉 All theme files have been processed!")

if __name__ == "__main__":
    main()
