#!/usr/bin/env python3
"""
Script to generate curated content with themes, content sets, and task items.

This script generates:
- 20 themes with proper content
- 2 content sets per theme (each with script and linked questions)
- 5 questions per set based on the script

Database: mongodb://localhost:27017/
"""

import asyncio
import random
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import List, Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"  # Default tenant database
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")  # Default user ID

class CuratedContentGenerator:
    def __init__(self, db_url: str, db_name: str):
        self.client = AsyncMongoClient(db_url)
        self.db = self.client[db_name]
        
    async def close(self):
        """Close database connection."""
        self.client.close()
    
    # Theme 1: Geography & Environment
    def create_geography_environment_theme(self) -> Dict[str, Any]:
        """Create Geography & Environment theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()
        
        theme = {
            "_id": theme_id,
            "name": "भूगोल र वातावरण",
            "name_en": "Geography & Environment",
            "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
            "description_en": "Nepal's geographical structure and environmental diversity",
            "icon": "🏔️",
            "color": "#2E8B57",
            "category": "भूगोल",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }
        
        # Content Set 1: Mountains and Rivers
        content_set_1_id = ObjectId()
        script_1 = """नेपाल हिमालयको काखमा बसेको एक सुन्दर देश हो। यहाँ संसारकै सबैभन्दा अग्लो हिमाल सगरमाथा छ जसको उचाइ ८,८४९ मिटर छ। नेपालमा तीन मुख्य भौगोलिक क्षेत्रहरू छन् - हिमाल, पहाड र तराई। यहाँका मुख्य नदीहरू कोशी, गण्डकी र कर्णाली हुन्। नेपालको सबैभन्दा लामो नदी कर्णाली हो। नेपालको सबैभन्दा ठूलो ताल रारा ताल हो जुन मुगु जिल्लामा अवस्थित छ।"""
        
        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "हिमाल र नदीहरू",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],  # Will be filled with task IDs
            "stories": [],
            "total_tasks": 10,
            "total_stories": 0,
            "text_tasks_ready": 10,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Mountains and rivers of Nepal",
            "submitted_at": None
        }
        
        # Task items for Content Set 1 - Updated to 10 items with proper difficulty
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "सगरमाथाको उचाइ",
                "question": {
                    "text": "स्क्रिप्ट अनुसार सगरमाथाको उचाइ कति मिटर छ?",
                    "translated_text": "According to the script, what is the height of Mount Everest in meters?",
                    "options": {
                        "a": "८,८४८ मिटर",
                        "b": "८,८४९ मिटर",
                        "c": "८,८५० मिटर",
                        "d": "८,८४७ मिटर"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "multiple_choice",
                "title": "नेपालका भौगोलिक क्षेत्र",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालका तीन मुख्य भौगोलिक क्षेत्रहरू कुन कुन हुन्?",
                    "translated_text": "According to the script, what are the three main geographical regions of Nepal?",
                    "options": {
                        "a": "हिमाल",
                        "b": "पहाड",
                        "c": "तराई",
                        "d": "मरुभूमि"
                    },
                    "answer_hint": "three regions mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको सबैभन्दा लामो नदी",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको सबैभन्दा लामो नदी कुन हो?",
                    "translated_text": "According to the script, which is the longest river in Nepal?",
                    "options": {
                        "a": "कोशी",
                        "b": "गण्डकी",
                        "c": "कर्णाली",
                        "d": "महाकाली"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "c", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको सबैभन्दा ठूलो ताल",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको सबैभन्दा ठूलो ताल कुन हो?",
                    "translated_text": "According to the script, which is the largest lake in Nepal?",
                    "options": {
                        "a": "फेवाताल",
                        "b": "रारा ताल",
                        "c": "तिलिचो ताल",
                        "d": "गोसाईकुण्ड"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "multiple_choice",
                "title": "नेपालका मुख्य नदीहरू",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालका मुख्य नदीहरू कुन कुन हुन्?",
                    "translated_text": "According to the script, what are the main rivers of Nepal?",
                    "options": {
                        "a": "कोशी",
                        "b": "गण्डकी",
                        "c": "कर्णाली",
                        "d": "गंगा"
                    },
                    "answer_hint": "three rivers mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        # Content Set 2: National Parks and Climate
        content_set_2_id = ObjectId()
        script_2 = """नेपालमा धेरै राष्ट्रिय निकुञ्जहरू छन्। चितवन राष्ट्रिय निकुञ्ज नेपालको पहिलो राष्ट्रिय निकुञ्ज हो जुन १९७३ मा स्थापना भएको थियो। सगरमाथा राष्ट्रिय निकुञ्ज सोलुखुम्बु जिल्लामा अवस्थित छ। नेपालको जलवायु मनसुनी प्रकारको छ र यहाँ मुख्यतः दक्षिण-पश्चिमी मनसुनबाट वर्षा हुन्छ। नेपालको कुल भूभागको ४४.७% भाग वन क्षेत्र छ।"""

        content_set_2 = {
            "_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "राष्ट्रिय निकुञ्ज र जलवायु",
            "input_type": "text",
            "input_content": {
                "script": script_2
            },
            "tasks": [],  # Will be filled with task IDs
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "National parks and climate of Nepal",
            "submitted_at": None
        }

        # Add task IDs to content sets
        content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]

        # Task items for Content Set 2
        task_items_2 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको पहिलो राष्ट्रिय निकुञ्ज",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको पहिलो राष्ट्रिय निकुञ्ज कुन हो?",
                    "translated_text": "According to the script, which is the first national park of Nepal?",
                    "options": {
                        "a": "सगरमाथा राष्ट्रिय निकुञ्ज",
                        "b": "चितवन राष्ट्रिय निकुञ्ज",
                        "c": "बर्दिया राष्ट्रिय निकुञ्ज",
                        "d": "लान्तङ राष्ट्रिय निकुञ्ज"
                    },
                    "answer_hint": "established in 1973",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको जलवायु",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको जलवायु कस्तो छ?",
                    "translated_text": "According to the script, what type of climate does Nepal have?",
                    "options": {
                        "a": "उष्णकटिबंधीय",
                        "b": "मनसुनी",
                        "c": "समशीतोष्ण",
                        "d": "शुष्क"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "चितवन राष्ट्रिय निकुञ्ज स्थापना",
                "question": {
                    "text": "स्क्रिप्ट अनुसार चितवन राष्ट्रिय निकुञ्ज कुन वर्ष स्थापना भएको थियो?",
                    "translated_text": "According to the script, in which year was Chitwan National Park established?",
                    "options": {
                        "a": "१९७२",
                        "b": "१९७३",
                        "c": "१९७४",
                        "d": "१९७५"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "सगरमाथा राष्ट्रिय निकुञ्ज स्थान",
                "question": {
                    "text": "स्क्रिप्ट अनुसार सगरमाथा राष्ट्रिय निकुञ्ज कुन जिल्लामा अवस्थित छ?",
                    "translated_text": "According to the script, in which district is Sagarmatha National Park located?",
                    "options": {
                        "a": "संखुवासभा",
                        "b": "सोलुखुम्बु",
                        "c": "तापलेजुङ",
                        "d": "दोलखा"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको वन क्षेत्र",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको कुल भूभागको कति प्रतिशत वन क्षेत्र छ?",
                    "translated_text": "According to the script, what percentage of Nepal's total land is forest area?",
                    "options": {
                        "a": "४०.५%",
                        "b": "४४.७%",
                        "c": "४५%",
                        "d": "५०%"
                    },
                    "answer_hint": "exact percentage mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        # Add task IDs to content sets
        content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
        content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]

        return {
            "theme": theme,
            "content_sets": [content_set_1, content_set_2],
            "task_items": task_items_1 + task_items_2
        }

    # Theme 2: Nepali Culture
    def create_nepali_culture_theme(self) -> Dict[str, Any]:
        """Create Nepali Culture theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "नेपाली संस्कृति",
            "name_en": "Nepali Culture",
            "description": "नेपाली संस्कृति र परम्पराका बारेमा",
            "description_en": "About Nepali culture and traditions",
            "icon": "🏛️",
            "color": "#FF6B6B",
            "category": "संस्कृति",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: National Symbols
        content_set_1_id = ObjectId()
        script_1 = """नेपाल एक सांस्कृतिक रूपमा धनी देश हो। यहाँको राष्ट्रिय पशु एक सिङ्गे गैंडा हो जुन चितवन र बर्दिया राष्ट्रिय निकुञ्जमा पाइन्छ। नेपालको राष्ट्रिय फूल गुराँस हो जुन हिमालमा फुल्छ। नेपालको राष्ट्रिय चरा डाँफे हो जुन रंगबिरंगी र सुन्दर छ। नेपाली झण्डा संसारमा एक मात्र त्रिकोणाकार झण्डा हो जसमा रातो र निलो रङ छ। नेपालको राष्ट्रिय गान "सयौं थुंगा फूलका हामी" बाट सुरु हुन्छ।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "राष्ट्रिय प्रतीकहरू",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "National symbols of Nepal",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको राष्ट्रिय पशु",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय पशु कुन हो?",
                    "translated_text": "According to the script, what is the national animal of Nepal?",
                    "options": {
                        "a": "बाघ",
                        "b": "एक सिङ्गे गैंडा",
                        "c": "हात्ती",
                        "d": "हिमाली भालु"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको राष्ट्रिय फूल",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय फूल कुन हो?",
                    "translated_text": "According to the script, what is the national flower of Nepal?",
                    "options": {
                        "a": "गुलाफ",
                        "b": "गुराँस",
                        "c": "सुनखरी",
                        "d": "कमल"
                    },
                    "answer_hint": "blooms in the Himalayas",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको राष्ट्रिय चरा",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय चरा कुन हो?",
                    "translated_text": "According to the script, what is the national bird of Nepal?",
                    "options": {
                        "a": "मयूर",
                        "b": "डाँफे",
                        "c": "चील",
                        "d": "कागा"
                    },
                    "answer_hint": "colorful and beautiful",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "multiple_choice",
                "title": "नेपाली झण्डाका रङहरू",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपाली झण्डामा कुन कुन रङहरू छन्?",
                    "translated_text": "According to the script, which colors are in the Nepali flag?",
                    "options": {
                        "a": "रातो",
                        "b": "निलो",
                        "c": "सेतो",
                        "d": "हरियो"
                    },
                    "answer_hint": "two colors mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b"], "type": "multiple"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको राष्ट्रिय गान",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय गान कुन शब्दबाट सुरु हुन्छ?",
                    "translated_text": "According to the script, which words does Nepal's national anthem start with?",
                    "options": {
                        "a": "सयौं थुंगा फूलका हामी",
                        "b": "रत्न हाम्रो मुलुक नेपाल",
                        "c": "हाम्रो नेपाल प्यारो नेपाल",
                        "d": "जय नेपाल जय नेपाल"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "a", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        # Content Set 2: Traditional Dress and Customs
        content_set_2_id = ObjectId()
        script_2 = """नेपाली संस्कृतिमा पारम्परिक पोशाकको विशेष महत्व छ। पुरुषहरूले दौरा सुरुवाल र धाका टोपी लगाउँछन्। महिलाहरूले गुन्यु चोलो लगाउँछन्। नेपालीहरूले हात जोडेर "नमस्कार" भन्छन् जसको अर्थ "तपाईंलाई सम्मान गर्छु" हो। खुकुरी नेपालको वीरता र साहसको प्रतीक हो। नेपालमा दशैं, तिहार र होली जस्ता मुख्य चाडपर्वहरू मनाइन्छन्।"""

        content_set_2 = {
            "_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "पारम्परिक पोशाक र रीतिरिवाज",
            "input_type": "text",
            "input_content": {
                "script": script_2
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Traditional dress and customs of Nepal",
            "submitted_at": None
        }

        # Task items for Content Set 2
        task_items_2 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "multiple_choice",
                "title": "नेपाली पुरुषको पारम्परिक पोशाक",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपाली पुरुषको पारम्परिक पोशाक कुन कुन हुन्?",
                    "translated_text": "According to the script, what are the traditional dresses for Nepali men?",
                    "options": {
                        "a": "दौरा सुरुवाल",
                        "b": "धाका टोपी",
                        "c": "पत्लून सर्ट",
                        "d": "जिन्स"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b"], "type": "multiple"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_2_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपाली महिलाको पारम्परिक पोशाक",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपाली महिलाको पारम्परिक पोशाक कुन हो?",
                    "translated_text": "According to the script, what is the traditional dress for Nepali women?",
                    "options": {
                        "a": "साडी",
                        "b": "गुन्यु चोलो",
                        "c": "कुर्ता",
                        "d": "लहंगा"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        # Add task IDs to content sets
        content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
        content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]

        return {
            "theme": theme,
            "content_sets": [content_set_1, content_set_2],
            "task_items": task_items_1 + task_items_2
        }

    # Theme 3: History & Heritage
    def create_history_heritage_theme(self) -> Dict[str, Any]:
        """Create History & Heritage theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "इतिहास र विरासत",
            "name_en": "History & Heritage",
            "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक विरासत",
            "description_en": "Nepal's historical events and cultural heritage",
            "icon": "📜",
            "color": "#8B4513",
            "category": "इतिहास",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: Ancient History
        content_set_1_id = ObjectId()
        script_1 = """नेपालको इतिहास धेरै पुरानो छ। लिच्छवि काल नेपालको स्वर्ण युग मानिन्छ जुन ४०० देखि ७५० ईस्वी सम्म चलेको थियो। यस समयमा मन्दिर र मूर्तिकलाको विकास भएको थियो। त्यसपछि मल्ल काल आयो जुन १२०० देखि १७६९ ईस्वी सम्म चलेको थियो। मल्ल कालमा काठमाडौं, पाटन र भक्तपुरमा तीन राज्यहरू थिए। यस समयमा न्यूवार कला र संस्कृतिको विकास भएको थियो।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "प्राचीन इतिहास",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Ancient history of Nepal",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको स्वर्ण युग",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको स्वर्ण युग कुन काल मानिन्छ?",
                    "translated_text": "According to the script, which period is considered the golden age of Nepal?",
                    "options": {
                        "a": "मल्ल काल",
                        "b": "लिच्छवि काल",
                        "c": "शाह काल",
                        "d": "राणा काल"
                    },
                    "answer_hint": "mentioned as golden age in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "लिच्छवि काल अवधि",
                "question": {
                    "text": "स्क्रिप्ट अनुसार लिच्छवि काल कुन समयदेखि कुन समयसम्म चलेको थियो?",
                    "translated_text": "According to the script, from when to when did the Licchavi period last?",
                    "options": {
                        "a": "३०० देखि ६५० ईस्वी",
                        "b": "४०० देखि ७५० ईस्वी",
                        "c": "५०० देखि ८०० ईस्वी",
                        "d": "६०० देखि ९०० ईस्वी"
                    },
                    "answer_hint": "exact period mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        return {
            "theme": theme,
            "content_sets": [content_set_1],
            "task_items": task_items_1
        }

    # Theme 4: Language & Literature
    def create_language_literature_theme(self) -> Dict[str, Any]:
        """Create Language & Literature theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "भाषा र साहित्य",
            "name_en": "Language & Literature",
            "description": "नेपाली भाषा र साहित्यका विविध पक्षहरू",
            "description_en": "Various aspects of Nepali language and literature",
            "icon": "📚",
            "color": "#4169E1",
            "category": "भाषा",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: Nepali Language
        content_set_1_id = ObjectId()
        script_1 = """नेपाली भाषा इन्डो-आर्यन भाषा परिवारको सदस्य हो। यो नेपालको राजभाषा हो र भारतको सिक्किम राज्यमा पनि आधिकारिक भाषा हो। नेपाली भाषा देवनागरी लिपिमा लेखिन्छ। यस भाषामा ३६ वटा अक्षरहरू छन् - ११ वटा स्वर र २५ वटा व्यञ्जन। नेपाली भाषाका प्रसिद्ध कविहरूमा भानुभक्त आचार्य, लक्ष्मीप्रसाद देवकोटा र बालकृष्ण सम पर्छन्। भानुभक्त आचार्यलाई आदिकवि भनिन्छ।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "नेपाली भाषा",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Nepali language and script",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपाली भाषाको लिपि",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपाली भाषा कुन लिपिमा लेखिन्छ?",
                    "translated_text": "According to the script, in which script is Nepali language written?",
                    "options": {
                        "a": "रोमन लिपि",
                        "b": "देवनागरी लिपि",
                        "c": "अरबी लिपि",
                        "d": "तिब्बती लिपि"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "आदिकवि",
                "question": {
                    "text": "स्क्रिप्ट अनुसार कसलाई आदिकवि भनिन्छ?",
                    "translated_text": "According to the script, who is called the first poet (Adikavi)?",
                    "options": {
                        "a": "लक्ष्मीप्रसाद देवकोटा",
                        "b": "भानुभक्त आचार्य",
                        "c": "बालकृष्ण सम",
                        "d": "मोतीराम भट्ट"
                    },
                    "answer_hint": "mentioned as Adikavi in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        return {
            "theme": theme,
            "content_sets": [content_set_1],
            "task_items": task_items_1
        }

    # Theme 5: Science & Technology
    def create_science_technology_theme(self) -> Dict[str, Any]:
        """Create Science & Technology theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "विज्ञान र प्रविधि",
            "name_en": "Science & Technology",
            "description": "आधुनिक विज्ञान र प्रविधिका बारेमा",
            "description_en": "About modern science and technology",
            "icon": "🔬",
            "color": "#00CED1",
            "category": "विज्ञान",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: Basic Science
        content_set_1_id = ObjectId()
        script_1 = """विज्ञान हाम्रो दैनिक जीवनको महत्वपूर्ण भाग हो। भौतिक विज्ञानले गुरुत्वाकर्षण, प्रकाश र ध्वनिको अध्ययन गर्छ। रसायन विज्ञानले तत्वहरू र यौगिकहरूको अध्ययन गर्छ। जीव विज्ञानले जीवित प्राणीहरूको अध्ययन गर्छ। आधुनिक प्रविधिमा कम्प्युटर, इन्टरनेट र मोबाइल फोन पर्छन्। नेपालमा पनि सूचना प्रविधिको विकास भइरहेको छ। विज्ञान र प्रविधिले मानव जीवनलाई सजिलो बनाएको छ।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "आधारभूत विज्ञान",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Basic science concepts",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "multiple_choice",
                "title": "विज्ञानका शाखाहरू",
                "question": {
                    "text": "स्क्रिप्ट अनुसार विज्ञानका मुख्य शाखाहरू कुन कुन हुन्?",
                    "translated_text": "According to the script, what are the main branches of science?",
                    "options": {
                        "a": "भौतिक विज्ञान",
                        "b": "रसायन विज्ञान",
                        "c": "जीव विज्ञान",
                        "d": "भूगोल विज्ञान"
                    },
                    "answer_hint": "three branches mentioned in script",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            },
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "जीव विज्ञानको अध्ययन",
                "question": {
                    "text": "स्क्रिप्ट अनुसार जीव विज्ञानले के को अध्ययन गर्छ?",
                    "translated_text": "According to the script, what does biology study?",
                    "options": {
                        "a": "तत्वहरू र यौगिकहरू",
                        "b": "जीवित प्राणीहरू",
                        "c": "गुरुत्वाकर्षण र प्रकाश",
                        "d": "कम्प्युटर र इन्टरनेट"
                    },
                    "answer_hint": "mentioned in the script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        return {
            "theme": theme,
            "content_sets": [content_set_1],
            "task_items": task_items_1
        }

    # Theme 6: Sports
    def create_sports_theme(self) -> Dict[str, Any]:
        """Create Sports theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "खेलकुद",
            "name_en": "Sports",
            "description": "नेपाली र अन्तर्राष्ट्रिय खेलकुदका बारेमा",
            "description_en": "About Nepali and international sports",
            "icon": "⚽",
            "color": "#32CD32",
            "category": "खेलकुद",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: Nepali Sports
        content_set_1_id = ObjectId()
        script_1 = """नेपालमा विभिन्न प्रकारका खेलकुदहरू खेलिन्छन्। फुटबल नेपालको सबैभन्दा लोकप्रिय खेल हो। क्रिकेट पनि धेरै मन पराइन्छ। नेपालको राष्ट्रिय खेल भलिबल हो। पारम्परिक खेलहरूमा दण्डी बियो, लुकामारी र कबड्डी पर्छन्। नेपालले ओलम्पिकमा पनि भाग लिएको छ। तेक्वान्दो, जुडो र एथलेटिक्समा नेपाली खेलाडीहरूले राम्रो प्रदर्शन गरेका छन्।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "नेपाली खेलकुद",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Nepali sports and games",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको राष्ट्रिय खेल",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय खेल कुन हो?",
                    "translated_text": "According to the script, what is the national sport of Nepal?",
                    "options": {
                        "a": "फुटबल",
                        "b": "क्रिकेट",
                        "c": "भलिबल",
                        "d": "कबड्डी"
                    },
                    "answer_hint": "mentioned as national sport in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "c", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        return {
            "theme": theme,
            "content_sets": [content_set_1],
            "task_items": task_items_1
        }

    # Theme 7: Festivals
    def create_festivals_theme(self) -> Dict[str, Any]:
        """Create Festivals theme with content sets and task items."""
        current_time = datetime.now(timezone.utc)
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": "चाडपर्व",
            "name_en": "Festivals",
            "description": "नेपालका विविध चाडपर्वहरू र तिनका महत्व",
            "description_en": "Various festivals of Nepal and their significance",
            "icon": "🎉",
            "color": "#FF69B4",
            "category": "चाडपर्व",
            "is_active": True,
            "created_at": current_time,
            "updated_at": current_time
        }

        # Content Set 1: Major Festivals
        content_set_1_id = ObjectId()
        script_1 = """नेपालमा वर्षभरि विभिन्न चाडपर्वहरू मनाइन्छन्। दशैं नेपालको सबैभन्दा ठूलो चाड हो जुन १५ दिनसम्म मनाइन्छ। तिहार प्रकाशको चाड हो जुन ५ दिनसम्म चल्छ। होली रङहरूको चाड हो। छठ पर्व सूर्य देवताको पूजा गरिन्छ। तीज महिलाहरूको मुख्य चाड हो। जनै पूर्णिमामा जनै फेरिन्छ। यी चाडपर्वहरूले नेपाली समाजलाई एकताबद्ध राख्छन्।"""

        content_set_1 = {
            "_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": "मुख्य चाडपर्वहरू",
            "input_type": "text",
            "input_content": {
                "script": script_1
            },
            "tasks": [],
            "stories": [],
            "total_tasks": 5,
            "total_stories": 0,
            "text_tasks_ready": 5,
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": 50,
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "theme_id": theme_id,
            "updated_at": current_time,
            "completed_at": None,
            "remark": "Major festivals of Nepal",
            "submitted_at": None
        }

        # Task items for Content Set 1
        task_items_1 = [
            {
                "_id": ObjectId(),
                "task_set_id": content_set_1_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": "single_choice",
                "title": "नेपालको सबैभन्दा ठूलो चाड",
                "question": {
                    "text": "स्क्रिप्ट अनुसार नेपालको सबैभन्दा ठूलो चाड कुन हो?",
                    "translated_text": "According to the script, which is the biggest festival of Nepal?",
                    "options": {
                        "a": "तिहार",
                        "b": "दशैं",
                        "c": "होली",
                        "d": "तीज"
                    },
                    "answer_hint": "mentioned as biggest festival in script",
                    "metadata": {}
                },
                "correct_answer": {"value": "b", "type": "single"},
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "metadata": {"theme_id": str(theme_id)},
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
        ]

        return {
            "theme": theme,
            "content_sets": [content_set_1],
            "task_items": task_items_1
        }

    async def generate_all_content(self):
        """Generate all themes, content sets, and task items."""
        print("🚀 Starting curated content generation...")

        all_themes = []
        all_content_sets = []
        all_task_items = []

        # Generate Geography & Environment theme
        print("📝 Generating Geography & Environment theme...")
        geo_data = self.create_geography_environment_theme()
        all_themes.append(geo_data["theme"])
        all_content_sets.extend(geo_data["content_sets"])
        all_task_items.extend(geo_data["task_items"])

        # Generate Nepali Culture theme
        print("📝 Generating Nepali Culture theme...")
        culture_data = self.create_nepali_culture_theme()
        all_themes.append(culture_data["theme"])
        all_content_sets.extend(culture_data["content_sets"])
        all_task_items.extend(culture_data["task_items"])

        # Generate History & Heritage theme
        print("📝 Generating History & Heritage theme...")
        history_data = self.create_history_heritage_theme()
        all_themes.append(history_data["theme"])
        all_content_sets.extend(history_data["content_sets"])
        all_task_items.extend(history_data["task_items"])

        # Generate Language & Literature theme
        print("📝 Generating Language & Literature theme...")
        language_data = self.create_language_literature_theme()
        all_themes.append(language_data["theme"])
        all_content_sets.extend(language_data["content_sets"])
        all_task_items.extend(language_data["task_items"])

        # Generate Science & Technology theme
        print("📝 Generating Science & Technology theme...")
        science_data = self.create_science_technology_theme()
        all_themes.append(science_data["theme"])
        all_content_sets.extend(science_data["content_sets"])
        all_task_items.extend(science_data["task_items"])

        # Generate Sports theme
        print("📝 Generating Sports theme...")
        sports_data = self.create_sports_theme()
        all_themes.append(sports_data["theme"])
        all_content_sets.extend(sports_data["content_sets"])
        all_task_items.extend(sports_data["task_items"])

        # Generate Festivals theme
        print("📝 Generating Festivals theme...")
        festivals_data = self.create_festivals_theme()
        all_themes.append(festivals_data["theme"])
        all_content_sets.extend(festivals_data["content_sets"])
        all_task_items.extend(festivals_data["task_items"])

        # Insert all themes
        await self.db.themes.insert_many(all_themes)
        print(f"✅ {len(all_themes)} themes inserted")

        # Insert all content sets
        await self.db.curated_content_set.insert_many(all_content_sets)
        print(f"✅ {len(all_content_sets)} content sets inserted")

        # Insert all task items
        await self.db.curated_content_items.insert_many(all_task_items)
        print(f"✅ {len(all_task_items)} task items inserted")

        print("\n🎉 Content generation completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Themes: {len(all_themes)}")
        print(f"   - Content Sets: {len(all_content_sets)}")
        print(f"   - Task Items: {len(all_task_items)}")


async def main():
    """Main function to run the content generation."""
    generator = CuratedContentGenerator(DB_URL, DB_NAME)

    try:
        # Clear existing data (optional)
        print("🧹 Clearing existing curated content...")
        await generator.db.themes.delete_many({})
        await generator.db.curated_content_set.delete_many({})
        await generator.db.curated_content_items.delete_many({})
        print("✅ Existing data cleared")

        # Generate new content
        await generator.generate_all_content()

    except Exception as e:
        print(f"❌ Error during generation: {e}")
        raise
    finally:
        await generator.close()


if __name__ == "__main__":
    asyncio.run(main())
