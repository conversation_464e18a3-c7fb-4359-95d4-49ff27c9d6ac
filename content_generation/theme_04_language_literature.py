#!/usr/bin/env python3
"""
Theme 4: Language & Literature (भाषा र साहित्य)
- 2 content sets with 10 task items total (5 per set)
- Real-world questions with proper difficulty levels
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_language_literature_theme() -> Dict[str, Any]:
    """Create Language & Literature theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "भाषा र साहित्य",
        "name_en": "Language & Literature",
        "description": "नेपाली भाषा र साहित्यका विविध पक्षहरू",
        "description_en": "Various aspects of Nepali language and literature",
        "icon": "📚",
        "color": "#4169E1",
        "category": "भाषा",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Nepali Language
    content_set_1_id = ObjectId()
    script_1 = """नेपाली भाषा इन्डो-आर्यन भाषा परिवारको सदस्य हो। यो नेपालको राजभाषा हो र भारतको सिक्किम राज्यमा पनि आधिकारिक भाषा हो। नेपाली भाषा देवनागरी लिपिमा लेखिन्छ। यस भाषामा ३६ वटा अक्षरहरू छन् - ११ वटा स्वर र २५ वटा व्यञ्जन। नेपाली भाषाका प्रसिद्ध कविहरूमा भानुभक्त आचार्य, लक्ष्मीप्रसाद देवकोटा र बालकृष्ण सम पर्छन्। भानुभक्त आचार्यलाई आदिकवि भनिन्छ किनभने उनले पहिलो पटक रामायणलाई नेपाली भाषामा अनुवाद गरेका थिए। नेपाली भाषामा बोल्ने मानिसहरूको संख्या विश्वभर करिब १७ मिलियन छ।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "नेपाली भाषा",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "tasks": [],
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Nepali language and script",
        "submitted_at": None
    }
    
    # Content Set 2: Literature and Poetry
    content_set_2_id = ObjectId()
    script_2 = """नेपाली साहित्यमा कविता, कथा, उपन्यास र नाटकका धेरै उत्कृष्ट कृतिहरू छन्। लक्ष्मीप्रसाद देवकोटालाई महाकवि भनिन्छ र उनको "मुना मदन" एक प्रसिद्ध खण्डकाव्य हो जुन १९३६ मा प्रकाशित भएको थियो। बालकृष्ण समको "गोरे" एक प्रसिद्ध कविता हो। नेपाली साहित्यमा पहिलो उपन्यास "रुक्मांगद" हो जुन चन्द्रकान्त देवकोटाले लेखेका थिए। नेपाली साहित्यमा महिला लेखिकाहरूमा पारिजात र बन्धना ढकाल प्रसिद्ध छिन्। पारिजातको "शिरीषको फूल" उपन्यासले मदन पुरस्कार जितेको थियो। नेपाली साहित्यको सबैभन्दा प्रतिष्ठित पुरस्कार मदन पुरस्कार हो।"""
    
    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "साहित्य र कविता",
        "input_type": "text",
        "input_content": {
            "script": script_2
        },
        "tasks": [],
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Literature and poetry in Nepali",
        "submitted_at": None
    }
    
    # Task items for Content Set 1 (5 items with varying difficulty)
    task_items_1 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली भाषाको लिपि",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली भाषा कुन लिपिमा लेखिन्छ?",
                "translated_text": "According to the script, in which script is Nepali language written?",
                "options": {
                    "a": "रोमन लिपि",
                    "b": "देवनागरी लिपि",
                    "c": "अरबी लिपि",
                    "d": "तिब्बती लिपि"
                },
                "answer_hint": "स्क्रिप्टमा लिपिको नाम स्पष्ट रूपमा उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "आदिकवि र कारण",
            "question": {
                "text": "स्क्रिप्ट अनुसार भानुभक्त आचार्यलाई आदिकवि किन भनिन्छ?",
                "translated_text": "According to the script, why is Bhanubhakta Acharya called the first poet (Adikavi)?",
                "options": {
                    "a": "उनले पहिलो कविता लेखेकाले",
                    "b": "उनले रामायणलाई नेपाली भाषामा अनुवाद गरेकाले",
                    "c": "उनले पहिलो उपन्यास लेखेकाले",
                    "d": "उनले पहिलो नाटक लेखेकाले"
                },
                "answer_hint": "स्क्रिप्टमा आदिकवि भनिने कारण उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - requires understanding reason
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली भाषाका अक्षरहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली भाषामा कुल कति वटा अक्षरहरू छन्?",
                "translated_text": "According to the script, how many letters are there in total in the Nepali language?",
                "options": {
                    "a": "३४ वटा",
                    "b": "३६ वटा",
                    "c": "३८ वटा",
                    "d": "४० वटा"
                },
                "answer_hint": "स्क्रिप्टमा कुल अक्षरहरूको संख्या उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific number
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "स्वर र व्यञ्जनको संख्या",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली भाषामा कति वटा स्वर र कति वटा व्यञ्जन छन्?",
                "translated_text": "According to the script, how many vowels and consonants are there in the Nepali language?",
                "options": {
                    "a": "१० वटा स्वर र २६ वटा व्यञ्जन",
                    "b": "११ वटा स्वर र २५ वटा व्यञ्जन",
                    "c": "१२ वटा स्वर र २४ वटा व्यञ्जन",
                    "d": "९ वटा स्वर र २७ वटा व्यञ्जन"
                },
                "answer_hint": "स्क्रिप्टमा स्वर र व्यञ्जनको सटीक संख्या उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - requires remembering two numbers
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली भाषी जनसंख्या",
            "question": {
                "text": "स्क्रिप्ट अनुसार विश्वभर नेपाली भाषामा बोल्ने मानिसहरूको संख्या करिब कति छ?",
                "translated_text": "According to the script, approximately how many people speak Nepali language worldwide?",
                "options": {
                    "a": "१५ मिलियन",
                    "b": "१७ मिलियन",
                    "c": "२० मिलियन",
                    "d": "२५ मिलियन"
                },
                "answer_hint": "स्क्रिप्टमा विश्वव्यापी नेपाली भाषीको संख्या उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific statistic
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]
    
    # Task items for Content Set 2 (5 items with varying difficulty)
    task_items_2 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "महाकवि",
            "question": {
                "text": "स्क्रिप्ट अनुसार कसलाई महाकवि भनिन्छ?",
                "translated_text": "According to the script, who is called the great poet (Mahakavi)?",
                "options": {
                    "a": "भानुभक्त आचार्य",
                    "b": "लक्ष्मीप्रसाद देवकोटा",
                    "c": "बालकृष्ण सम",
                    "d": "चन्द्रकान्त देवकोटा"
                },
                "answer_hint": "स्क्रिप्टमा महाकवि भनिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "मुना मदन प्रकाशन वर्ष",
            "question": {
                "text": "स्क्रिप्ट अनुसार 'मुना मदन' खण्डकाव्य कुन वर्ष प्रकाशित भएको थियो?",
                "translated_text": "According to the script, in which year was the epic poem 'Muna Madan' published?",
                "options": {
                    "a": "१९३४",
                    "b": "१९३६",
                    "c": "१९३८",
                    "d": "१९४०"
                },
                "answer_hint": "स्क्रिप्टमा प्रकाशन वर्ष उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific year
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली साहित्यको पहिलो उपन्यास",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली साहित्यको पहिलो उपन्यास कुन हो र कसले लेखेको हो?",
                "translated_text": "According to the script, what is the first novel in Nepali literature and who wrote it?",
                "options": {
                    "a": "मुना मदन - लक्ष्मीप्रसाद देवकोटा",
                    "b": "रुक्मांगद - चन्द्रकान्त देवकोटा",
                    "c": "शिरीषको फूल - पारिजात",
                    "d": "गोरे - बालकृष्ण सम"
                },
                "answer_hint": "स्क्रिप्टमा पहिलो उपन्यास र लेखकको नाम उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - requires two facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "मदन पुरस्कार विजेता",
            "question": {
                "text": "स्क्रिप्ट अनुसार पारिजातको कुन उपन्यासले मदन पुरस्कार जितेको थियो?",
                "translated_text": "According to the script, which novel by Parijat won the Madan Prize?",
                "options": {
                    "a": "बाटो नभएको ठाउँ",
                    "b": "शिरीषको फूल",
                    "c": "अनि उसले भन्यो",
                    "d": "मुटुको कुरा"
                },
                "answer_hint": "स्क्रिप्टमा मदन पुरस्कार जित्ने उपन्यासको नाम उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific work
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली साहित्यको प्रतिष्ठित पुरस्कार",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली साहित्यको सबैभन्दा प्रतिष्ठित पुरस्कार कुन हो?",
                "translated_text": "According to the script, what is the most prestigious award in Nepali literature?",
                "options": {
                    "a": "भानुभक्त पुरस्कार",
                    "b": "मदन पुरस्कार",
                    "c": "देवकोटा पुरस्कार",
                    "d": "साहित्य अकादमी पुरस्कार"
                },
                "answer_hint": "स्क्रिप्टमा सबैभन्दा प्रतिष्ठित पुरस्कारको नाम उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]
    
    # Add task IDs to content sets
    content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
    content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]
    
    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "task_items": task_items_1 + task_items_2
    }

if __name__ == "__main__":
    data = create_language_literature_theme()
    print(f"Created theme: {data['theme']['name']}")
    print(f"Content sets: {len(data['content_sets'])}")
    print(f"Task items: {len(data['task_items'])}")
