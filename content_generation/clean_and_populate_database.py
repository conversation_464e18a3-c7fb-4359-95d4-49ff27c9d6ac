#!/usr/bin/env python3
"""
Clean database and populate with all theme data (themes 1-11)
"""

import asyncio
import importlib.util
import sys
import os
from pymongo import AsyncMongoClient

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"

async def clean_database():
    """Clean existing theme-related collections."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]
    
    try:
        print("🧹 Cleaning database...")
        
        # Drop collections
        await db.themes.drop()
        await db.curated_content_sets.drop()
        await db.tasks.drop()
        
        print("✅ Database cleaned successfully!")

    except Exception as e:
        print(f"❌ Error cleaning database: {e}")
    finally:
        await client.close()

async def load_and_insert_theme(theme_file):
    """Load a theme file and insert its data."""
    try:
        # Import the theme module
        spec = importlib.util.spec_from_file_location("theme_module", theme_file)
        theme_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(theme_module)
        
        # Get the theme creation function
        theme_functions = [attr for attr in dir(theme_module) if attr.startswith('create_') and attr.endswith('_theme')]
        
        if not theme_functions:
            print(f"❌ No theme creation function found in {os.path.basename(theme_file)}")
            return False
        
        # Call the theme creation function
        create_function = getattr(theme_module, theme_functions[0])
        theme_data = create_function()
        
        # Connect to database
        client = AsyncMongoClient(DB_URL)
        db = client[DB_NAME]
        
        try:
            # Insert theme
            await db.themes.insert_one(theme_data["theme"])
            
            # Insert content sets
            await db.curated_content_sets.insert_many(theme_data["content_sets"])
            
            # Insert tasks (handle both "tasks" and "task_items" keys)
            tasks_data = theme_data.get("tasks") or theme_data.get("task_items", [])
            await db.tasks.insert_many(tasks_data)

            theme_name = theme_data["theme"]["name"]
            content_sets_count = len(theme_data["content_sets"])
            tasks_count = len(tasks_data)
            
            print(f"✅ {os.path.basename(theme_file)}: {theme_name} ({content_sets_count} sets, {tasks_count} tasks)")
            return True
            
        except Exception as e:
            print(f"❌ Error inserting {os.path.basename(theme_file)}: {e}")
            return False
        finally:
            await client.close()
            
    except Exception as e:
        print(f"❌ Error loading {os.path.basename(theme_file)}: {e}")
        return False

async def populate_database():
    """Populate database with all theme data."""
    content_dir = "."  # Current directory should be content_generation
    
    # Find all theme files
    theme_files = []
    for filename in os.listdir(content_dir):
        if filename.startswith("theme_") and filename.endswith(".py") and filename != "theme_template.py":
            theme_files.append(os.path.join(content_dir, filename))
    
    theme_files.sort()
    
    print(f"🚀 Found {len(theme_files)} theme files to process...")
    print()
    
    success_count = 0
    for theme_file in theme_files:
        success = await load_and_insert_theme(theme_file)
        if success:
            success_count += 1
    
    print()
    print(f"🎉 Successfully inserted {success_count}/{len(theme_files)} themes!")
    
    return success_count

async def main():
    """Main function to clean and populate database."""
    print("🚀 Starting database cleanup and population...")
    print()
    
    # Clean database first
    await clean_database()
    print()
    
    # Populate with theme data
    success_count = await populate_database()
    
    print()
    if success_count > 0:
        print(f"✅ Database successfully populated with {success_count} themes!")
        print("📊 Collections created:")
        print("   - themes")
        print("   - curated_content_sets") 
        print("   - tasks")
    else:
        print("❌ No themes were successfully inserted!")

if __name__ == "__main__":
    asyncio.run(main())
