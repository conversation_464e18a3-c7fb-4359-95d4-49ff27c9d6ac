#!/usr/bin/env python3
"""
Theme 1: Geography & Environment
- 2 content sets with 10 task items each (5 per set)
- Real-world questions with proper difficulty levels
- Proper script-based questions
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_geography_environment_theme() -> Dict[str, Any]:
    """Create Geography & Environment theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "भूगोल र वातावरण",
        "name_en": "Geography & Environment",
        "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
        "description_en": "Nepal's geographical structure and environmental diversity",
        "icon": "🏔️",
        "color": "#2E8B57",
        "category": "भूगोल",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Mountains and Rivers
    content_set_1_id = ObjectId()
    script_1 = """नेपाल हिमालयको काखमा बसेको एक सुन्दर देश हो। यहाँ संसारकै सबैभन्दा अग्लो हिमाल सगरमाथा छ जसको उचाइ ८,८४९ मिटर छ। नेपालमा तीन मुख्य भौगोलिक क्षेत्रहरू छन् - हिमाल, पहाड र तराई। हिमाली क्षेत्र समुद्री सतहबाट ४,८७७ मिटरभन्दा माथि अवस्थित छ। यहाँका मुख्य नदीहरू कोशी, गण्डकी र कर्णाली हुन्। नेपालको सबैभन्दा लामो नदी कर्णाली हो जसको लम्बाइ ५०७ किलोमिटर छ। नेपालको सबैभन्दा ठूलो ताल रारा ताल हो जुन मुगु जिल्लामा अवस्थित छ र यसको क्षेत्रफल १०.८ वर्ग किलोमिटर छ।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "हिमाल र नदीहरू",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "tasks": [],  # Will be filled with task IDs
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Mountains and rivers of Nepal",
        "submitted_at": None
    }
    
    # Task items for Content Set 1 (5 items with varying difficulty)
    task_items_1 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "सगरमाथाको उचाइ",
            "question": {
                "text": "स्क्रिप्ट अनुसार सगरमाथाको उचाइ कति मिटर छ?",
                "translated_text": "According to the script, what is the height of Mount Everest in meters?",
                "options": {
                    "a": "८,८४८ मिटर",
                    "b": "८,८४९ मिटर", 
                    "c": "८,८५० मिटर",
                    "d": "८,८४७ मिटर"
                },
                "answer_hint": "स्क्रिप्टमा स्पष्ट रूपमा उल्लेख गरिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact from script
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "multiple_choice",
            "title": "नेपालका भौगोलिक क्षेत्रहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालका तीन मुख्य भौगोलिक क्षेत्रहरू कुन कुन हुन्?",
                "translated_text": "According to the script, what are the three main geographical regions of Nepal?",
                "options": {
                    "a": "हिमाल",
                    "b": "पहाड", 
                    "c": "तराई",
                    "d": "मरुभूमि"
                },
                "answer_hint": "स्क्रिप्टमा तीन क्षेत्रहरू उल्लेख गरिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - multiple choice requires understanding
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको सबैभन्दा लामो नदी",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको सबैभन्दा लामो नदी कुन हो र यसको लम्बाइ कति छ?",
                "translated_text": "According to the script, which is the longest river in Nepal and what is its length?",
                "options": {
                    "a": "कोशी - ४५० किलोमिटर",
                    "b": "गण्डकी - ४८० किलोमिटर",
                    "c": "कर्णाली - ५०७ किलोमिटर",
                    "d": "महाकाली - ३५० किलोमिटर"
                },
                "answer_hint": "स्क्रिप्टमा नदीको नाम र लम्बाइ दुवै उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "c", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - requires remembering two facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "हिमाली क्षेत्रको उचाइ",
            "question": {
                "text": "स्क्रिप्ट अनुसार हिमाली क्षेत्र समुद्री सतहबाट कति मिटरभन्दा माथि अवस्थित छ?",
                "translated_text": "According to the script, at what height above sea level is the Himalayan region located?",
                "options": {
                    "a": "४,५०० मिटरभन्दा माथि",
                    "b": "४,८७७ मिटरभन्दा माथि",
                    "c": "५,००० मिटरभन्दा माथि",
                    "d": "५,५०० मिटरभन्दा माथि"
                },
                "answer_hint": "स्क्रिप्टमा सटीक संख्या दिइएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific numerical fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "रारा तालको विवरण",
            "question": {
                "text": "स्क्रिप्ट अनुसार रारा ताल कुन जिल्लामा छ र यसको क्षेत्रफल कति छ?",
                "translated_text": "According to the script, in which district is Rara Lake located and what is its area?",
                "options": {
                    "a": "मुगु जिल्ला - १०.८ वर्ग किलोमिटर",
                    "b": "जुम्ला जिल्ला - १२.५ वर्ग किलोमिटर",
                    "c": "डोल्पा जिल्ला - ९.८ वर्ग किलोमिटर",
                    "d": "हुम्ला जिल्ला - ११.२ वर्ग किलोमिटर"
                },
                "answer_hint": "स्क्रिप्टमा जिल्ला र क्षेत्रफल दुवै उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "a", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - requires remembering multiple facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Content Set 2: National Parks and Climate
    content_set_2_id = ObjectId()
    script_2 = """नेपालमा धेरै राष्ट्रिय निकुञ्जहरू छन् जसले वन्यजन्तु र प्राकृतिक सम्पदाको संरक्षण गर्छन्। चितवन राष्ट्रिय निकुञ्ज नेपालको पहिलो राष्ट्रिय निकुञ्ज हो जुन १९७३ मा स्थापना भएको थियो र यसको क्षेत्रफल ९३२ वर्ग किलोमिटर छ। सगरमाथा राष्ट्रिय निकुञ्ज सोलुखुम्बु जिल्लामा अवस्थित छ र यो १९७६ मा स्थापना भएको थियो। नेपालको जलवायु मनसुनी प्रकारको छ र यहाँ मुख्यतः दक्षिण-पश्चिमी मनसुनबाट वर्षा हुन्छ। नेपालको कुल भूभागको ४४.७% भाग वन क्षेत्र छ। नेपालमा वार्षिक औसत वर्षा १,५०० मिलिमिटर हुन्छ।"""

    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "राष्ट्रिय निकुञ्ज र जलवायु",
        "input_type": "text",
        "input_content": {
            "script": script_2
        },
        "tasks": [],  # Will be filled with task IDs
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "National parks and climate of Nepal",
        "submitted_at": None
    }

    # Task items for Content Set 2 (5 items with varying difficulty)
    task_items_2 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको पहिलो राष्ट्रिय निकुञ्ज",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको पहिलो राष्ट्रिय निकुञ्ज कुन हो र कहिले स्थापना भएको थियो?",
                "translated_text": "According to the script, which is Nepal's first national park and when was it established?",
                "options": {
                    "a": "सगरमाथा राष्ट्रिय निकुञ्ज - १९७६",
                    "b": "चितवन राष्ट्रिय निकुञ्ज - १९७३",
                    "c": "बर्दिया राष्ट्रिय निकुञ्ज - १९७५",
                    "d": "लान्तङ राष्ट्रिय निकुञ्ज - १९७४"
                },
                "answer_hint": "स्क्रिप्टमा पहिलो निकुञ्ज र स्थापना वर्ष उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - requires two facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको जलवायु प्रकार",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको जलवायु कस्तो प्रकारको छ?",
                "translated_text": "According to the script, what type of climate does Nepal have?",
                "options": {
                    "a": "उष्णकटिबंधीय",
                    "b": "मनसुनी",
                    "c": "समशीतोष्ण",
                    "d": "शुष्क"
                },
                "answer_hint": "स्क्रिप्टमा जलवायुको प्रकार स्पष्ट रूपमा उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "चितवन राष्ट्रिय निकुञ्जको क्षेत्रफल",
            "question": {
                "text": "स्क्रिप्ट अनुसार चितवन राष्ट्रिय निकुञ्जको क्षेत्रफल कति छ?",
                "translated_text": "According to the script, what is the area of Chitwan National Park?",
                "options": {
                    "a": "८५० वर्ग किलोमिटर",
                    "b": "९३२ वर्ग किलोमिटर",
                    "c": "१,००० वर्ग किलोमिटर",
                    "d": "१,२०० वर्ग किलोमिटर"
                },
                "answer_hint": "स्क्रिप्टमा सटीक क्षेत्रफल दिइएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific numerical fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको वन क्षेत्र प्रतिशत",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको कुल भूभागको कति प्रतिशत वन क्षेत्र छ?",
                "translated_text": "According to the script, what percentage of Nepal's total land is forest area?",
                "options": {
                    "a": "४०.५%",
                    "b": "४४.७%",
                    "c": "४८.२%",
                    "d": "५२.१%"
                },
                "answer_hint": "स्क्रिप्टमा सटीक प्रतिशत उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific percentage
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको वार्षिक औसत वर्षा",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालमा वार्षिक औसत वर्षा कति हुन्छ?",
                "translated_text": "According to the script, what is the annual average rainfall in Nepal?",
                "options": {
                    "a": "१,२०० मिलिमिटर",
                    "b": "१,५०० मिलिमिटर",
                    "c": "१,८०० मिलिमिटर",
                    "d": "२,००० मिलिमिटर"
                },
                "answer_hint": "स्क्रिप्टमा वार्षिक औसत वर्षाको मात्रा दिइएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - specific measurement
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Add task IDs to content sets
    content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
    content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]

    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "task_items": task_items_1 + task_items_2
    }

if __name__ == "__main__":
    data = create_geography_environment_theme()
    print(f"Created theme: {data['theme']['name']}")
    print(f"Content sets: {len(data['content_sets'])}")
    print(f"Task items: {len(data['task_items'])}")
