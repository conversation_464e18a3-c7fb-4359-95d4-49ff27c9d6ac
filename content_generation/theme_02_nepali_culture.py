#!/usr/bin/env python3
"""
Theme 2: Nepali Culture
- 2 content sets with 10 task items each (5 per set)
- Real-world questions with proper difficulty levels
- Proper script-based questions
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_nepali_culture_theme() -> Dict[str, Any]:
    """Create Nepali Culture theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "description": "नेपाली संस्कृति र परम्पराका बारेमा",
        "description_en": "About Nepali culture and traditions",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "संस्कृति",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: National Symbols and Identity
    content_set_1_id = ObjectId()
    script_1 = """नेपाल एक सांस्कृतिक रूपमा धनी देश हो। यहाँको राष्ट्रिय पशु एक सिङ्गे गैंडा हो जुन चितवन र बर्दिया राष्ट्रिय निकुञ्जमा पाइन्छ। नेपालको राष्ट्रिय फूल गुराँस हो जुन हिमालमा फुल्छ र यसका ३० भन्दा बढी प्रजातिहरू छन्। नेपालको राष्ट्रिय चरा डाँफे हो जुन रंगबिरंगी र सुन्दर छ। नेपाली झण्डा संसारमा एक मात्र त्रिकोणाकार झण्डा हो जसमा रातो र निलो रङ छ। रातो रङले वीरताको प्रतीक गर्छ भने निलो रङले शान्तिको प्रतीक गर्छ। नेपालको राष्ट्रिय गान "सयौं थुंगा फूलका हामी" बाट सुरु हुन्छ जुन अमर सिंह थापाले लेखेका थिए।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "राष्ट्रिय प्रतीकहरू र पहिचान",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "tasks": [],  # Will be filled with task IDs
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "National symbols and identity of Nepal",
        "submitted_at": None
    }
    
    # Task items for Content Set 1 (5 items with varying difficulty)
    task_items_1 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको राष्ट्रिय पशु",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय पशु कुन हो?",
                "translated_text": "According to the script, what is the national animal of Nepal?",
                "options": {
                    "a": "बाघ",
                    "b": "एक सिङ्गे गैंडा",
                    "c": "हात्ती",
                    "d": "हिमाली भालु"
                },
                "answer_hint": "स्क्रिप्टमा स्पष्ट रूपमा उल्लेख गरिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - direct fact
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "गुराँसका प्रजातिहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार गुराँसका कति भन्दा बढी प्रजातिहरू छन्?",
                "translated_text": "According to the script, how many species of rhododendron are there?",
                "options": {
                    "a": "२० भन्दा बढी",
                    "b": "२५ भन्दा बढी",
                    "c": "३० भन्दा बढी",
                    "d": "३५ भन्दा बढी"
                },
                "answer_hint": "स्क्रिप्टमा प्रजातिहरूको संख्या उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "c", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific number
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "multiple_choice",
            "title": "नेपाली झण्डाका रङहरूको अर्थ",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली झण्डाका रातो र निलो रङले के प्रतीक गर्छन्?",
                "translated_text": "According to the script, what do the red and blue colors of the Nepali flag symbolize?",
                "options": {
                    "a": "रातो - वीरता",
                    "b": "निलो - शान्ति",
                    "c": "रातो - प्रेम",
                    "d": "निलो - आकाश"
                },
                "answer_hint": "स्क्रिप्टमा दुवै रङको अर्थ उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": ["a", "b"], "type": "multiple"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - multiple choice with meaning
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "राष्ट्रिय गानका लेखक",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको राष्ट्रिय गान कसले लेखेका थिए?",
                "translated_text": "According to the script, who wrote Nepal's national anthem?",
                "options": {
                    "a": "लक्ष्मीप्रसाद देवकोटा",
                    "b": "अमर सिंह थापा",
                    "c": "भानुभक्त आचार्य",
                    "d": "बालकृष्ण सम"
                },
                "answer_hint": "स्क्रिप्टमा लेखकको नाम उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - specific person's name
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपाली झण्डाको विशेषता",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपाली झण्डाको के विशेषता छ?",
                "translated_text": "According to the script, what is special about the Nepali flag?",
                "options": {
                    "a": "संसारको सबैभन्दा पुरानो झण्डा",
                    "b": "संसारमा एक मात्र त्रिकोणाकार झण्डा",
                    "c": "संसारको सबैभन्दा ठूलो झण्डा",
                    "d": "संसारको सबैभन्दा रंगीन झण्डा"
                },
                "answer_hint": "स्क्रिप्टमा झण्डाको आकारको बारेमा उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - unique feature
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Content Set 2: Traditional Dress and Customs
    content_set_2_id = ObjectId()
    script_2 = """नेपाली संस्कृतिमा पारम्परिक पोशाकको विशेष महत्व छ। पुरुषहरूले दौरा सुरुवाल र धाका टोपी लगाउँछन्। दौरा सुरुवालमा ८ वटा तार हुन्छन् जसले अष्टांग योगको प्रतीक गर्छ। महिलाहरूले गुन्यु चोलो लगाउँछन् जुन नेवार समुदायको पारम्परिक पोशाक हो। नेपालीहरूले हात जोडेर "नमस्कार" भन्छन् जसको अर्थ "तपाईंलाई सम्मान गर्छु" हो। खुकुरी नेपालको वीरता र साहसको प्रतीक हो र यो गोर्खाली सैनिकहरूको मुख्य हतियार हो। नेपालमा दशैं, तिहार र होली जस्ता मुख्य चाडपर्वहरू मनाइन्छन्। दशैं नेपालको सबैभन्दा ठूलो चाड हो जुन १५ दिनसम्म मनाइन्छ।"""

    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "पारम्परिक पोशाक र रीतिरिवाज",
        "input_type": "text",
        "input_content": {
            "script": script_2
        },
        "tasks": [],  # Will be filled with task IDs
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Traditional dress and customs of Nepal",
        "submitted_at": None
    }

    # Task items for Content Set 2 (5 items with varying difficulty)
    task_items_2 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "दौरा सुरुवालका तारहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार दौरा सुरुवालमा कति वटा तार हुन्छन् र यसले के प्रतीक गर्छ?",
                "translated_text": "According to the script, how many strings are there in daura suruwal and what does it symbolize?",
                "options": {
                    "a": "६ वटा तार - षड्दर्शनको प्रतीक",
                    "b": "८ वटा तार - अष्टांग योगको प्रतीक",
                    "c": "१० वटा तार - दशैंको प्रतीक",
                    "d": "१२ वटा तार - बाह्र महिनाको प्रतीक"
                },
                "answer_hint": "स्क्रिप्टमा तारको संख्या र अर्थ दुवै उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - requires two facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "गुन्यु चोलोको पहिचान",
            "question": {
                "text": "स्क्रिप्ट अनुसार गुन्यु चोलो कुन समुदायको पारम्परिक पोशाक हो?",
                "translated_text": "According to the script, gunyu cholo is the traditional dress of which community?",
                "options": {
                    "a": "गुरुङ समुदाय",
                    "b": "नेवार समुदाय",
                    "c": "तामाङ समुदाय",
                    "d": "मगर समुदाय"
                },
                "answer_hint": "स्क्रिप्टमा समुदायको नाम उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - specific community
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "खुकुरीको महत्व",
            "question": {
                "text": "स्क्रिप्ट अनुसार खुकुरी कसको मुख्य हतियार हो र यसले के प्रतीक गर्छ?",
                "translated_text": "According to the script, whose main weapon is the khukuri and what does it symbolize?",
                "options": {
                    "a": "राणा सैनिकहरूको - शक्तिको प्रतीक",
                    "b": "गोर्खाली सैनिकहरूको - वीरता र साहसको प्रतीक",
                    "c": "शाह सैनिकहरूको - एकताको प्रतीक",
                    "d": "मल्ल सैनिकहरूको - न्यायको प्रतीक"
                },
                "answer_hint": "स्क्रिप्टमा सैनिकहरूको नाम र प्रतीकको अर्थ उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 3,  # Hard - multiple facts
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "multiple_choice",
            "title": "नेपालका मुख्य चाडपर्वहरू",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालका मुख्य चाडपर्वहरू कुन कुन हुन्?",
                "translated_text": "According to the script, what are the main festivals of Nepal?",
                "options": {
                    "a": "दशैं",
                    "b": "तिहार",
                    "c": "होली",
                    "d": "छठ"
                },
                "answer_hint": "स्क्रिप्टमा तीन मुख्य चाडहरू उल्लेख छन्",
                "metadata": {}
            },
            "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,  # Medium - multiple choice
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "दशैंको अवधि",
            "question": {
                "text": "स्क्रिप्ट अनुसार दशैं कति दिनसम्म मनाइन्छ?",
                "translated_text": "According to the script, for how many days is Dashain celebrated?",
                "options": {
                    "a": "१० दिन",
                    "b": "१२ दिन",
                    "c": "१५ दिन",
                    "d": "२० दिन"
                },
                "answer_hint": "स्क्रिप्टमा दशैंको अवधि उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "c", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,  # Easy - specific number
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Add task IDs to content sets
    content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
    content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]

    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "task_items": task_items_1 + task_items_2
    }

if __name__ == "__main__":
    data = create_nepali_culture_theme()
    print(f"Created theme: {data['theme']['name']}")
    print(f"Content sets: {len(data['content_sets'])}")
    print(f"Task items: {len(data['task_items'])}")
