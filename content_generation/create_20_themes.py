#!/usr/bin/env python3
"""
Create 20 themes with 2 content sets each and 10 task items per theme.
Each theme has real-world questions with proper difficulty levels.
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any, List
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

class ComprehensiveThemeGenerator:
    def __init__(self):
        self.current_time = datetime.now(timezone.utc)
    
    def get_theme_templates(self) -> List[Dict]:
        """Return templates for all 20 themes."""
        return [
            {
                "name": "भूगोल र वातावरण", "name_en": "Geography & Environment",
                "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
                "description_en": "Nepal's geographical structure and environmental diversity",
                "icon": "🏔️", "color": "#2E8B57", "category": "भूगोल"
            },
            {
                "name": "नेपाली संस्कृति", "name_en": "Nepali Culture",
                "description": "नेपाली संस्कृति र परम्पराका बारेमा",
                "description_en": "About Nepali culture and traditions",
                "icon": "🏛️", "color": "#FF6B6B", "category": "संस्कृति"
            },
            {
                "name": "इतिहास र विरासत", "name_en": "History & Heritage",
                "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक विरासत",
                "description_en": "Nepal's historical events and cultural heritage",
                "icon": "📜", "color": "#8B4513", "category": "इतिहास"
            },
            {
                "name": "भाषा र साहित्य", "name_en": "Language & Literature",
                "description": "नेपाली भाषा र साहित्यका विविध पक्षहरू",
                "description_en": "Various aspects of Nepali language and literature",
                "icon": "📚", "color": "#4169E1", "category": "भाषा"
            },
            {
                "name": "पारम्परिक कला र शिल्प", "name_en": "Traditional Arts & Crafts",
                "description": "नेपालका पारम्परिक कला र शिल्पकलाहरू",
                "description_en": "Traditional arts and crafts of Nepal",
                "icon": "🎨", "color": "#FF8C00", "category": "कला"
            },
            {
                "name": "चाडपर्व र उत्सवहरू", "name_en": "Festivals & Celebrations",
                "description": "नेपालका विविध चाडपर्व र उत्सवहरू",
                "description_en": "Various festivals and celebrations of Nepal",
                "icon": "🎉", "color": "#FF1493", "category": "चाडपर्व"
            },
            {
                "name": "खानपान र व्यञ्जन", "name_en": "Food & Cuisine",
                "description": "नेपाली खानपान र पारम्परिक व्यञ्जनहरू",
                "description_en": "Nepali food and traditional cuisines",
                "icon": "🍛", "color": "#32CD32", "category": "खानपान"
            },
            {
                "name": "संगीत र नृत्य", "name_en": "Music & Dance",
                "description": "नेपालका पारम्परिक संगीत र नृत्यकलाहरू",
                "description_en": "Traditional music and dance forms of Nepal",
                "icon": "🎵", "color": "#9370DB", "category": "संगीत"
            },
            {
                "name": "खेलकुद र मनोरञ्जन", "name_en": "Sports & Recreation",
                "description": "नेपालका खेलकुद र मनोरञ्जनका साधनहरू",
                "description_en": "Sports and recreational activities in Nepal",
                "icon": "⚽", "color": "#FF4500", "category": "खेलकुद"
            },
            {
                "name": "अर्थतन्त्र र व्यापार", "name_en": "Economy & Trade",
                "description": "नेपालको आर्थिक अवस्था र व्यापारिक गतिविधिहरू",
                "description_en": "Economic condition and trade activities of Nepal",
                "icon": "💰", "color": "#FFD700", "category": "अर्थतन्त्र"
            },
            {
                "name": "राजनीति र शासन", "name_en": "Politics & Governance",
                "description": "नेपालको राजनीतिक व्यवस्था र शासन प्रणाली",
                "description_en": "Political system and governance of Nepal",
                "icon": "🏛️", "color": "#4682B4", "category": "राजनीति"
            },
            {
                "name": "धर्म र दर्शन", "name_en": "Religion & Philosophy",
                "description": "नेपालका धार्मिक परम्परा र दार्शनिक चिन्तनहरू",
                "description_en": "Religious traditions and philosophical thoughts of Nepal",
                "icon": "🕉️", "color": "#DC143C", "category": "धर्म"
            },
            {
                "name": "शिक्षा र ज्ञान", "name_en": "Education & Knowledge",
                "description": "नेपालको शिक्षा प्रणाली र ज्ञानका क्षेत्रहरू",
                "description_en": "Education system and knowledge areas of Nepal",
                "icon": "🎓", "color": "#2F4F4F", "category": "शिक्षा"
            },
            {
                "name": "स्वास्थ्य र चिकित्सा", "name_en": "Health & Medicine",
                "description": "नेपालको स्वास्थ्य सेवा र पारम्परिक चिकित्सा",
                "description_en": "Health services and traditional medicine of Nepal",
                "icon": "🏥", "color": "#228B22", "category": "स्वास्थ्य"
            },
            {
                "name": "प्रविधि र नवाचार", "name_en": "Technology & Innovation",
                "description": "नेपालमा प्रविधिको विकास र नवाचारहरू",
                "description_en": "Technology development and innovations in Nepal",
                "icon": "💻", "color": "#1E90FF", "category": "प्रविधि"
            },
            {
                "name": "यातायात र सञ्चार", "name_en": "Transport & Communication",
                "description": "नेपालको यातायात र सञ्चार व्यवस्था",
                "description_en": "Transportation and communication systems of Nepal",
                "icon": "🚗", "color": "#FF6347", "category": "यातायात"
            },
            {
                "name": "कृषि र पशुपालन", "name_en": "Agriculture & Livestock",
                "description": "नेपालको कृषि र पशुपालन क्षेत्र",
                "description_en": "Agriculture and livestock sector of Nepal",
                "icon": "🌾", "color": "#8FBC8F", "category": "कृषि"
            },
            {
                "name": "पर्यटन र होटल", "name_en": "Tourism & Hospitality",
                "description": "नेपालको पर्यटन उद्योग र आतिथ्य सेवा",
                "description_en": "Tourism industry and hospitality services of Nepal",
                "icon": "🏨", "color": "#20B2AA", "category": "पर्यटन"
            },
            {
                "name": "वातावरण र संरक्षण", "name_en": "Environment & Conservation",
                "description": "वातावरणीय संरक्षण र दिगो विकास",
                "description_en": "Environmental conservation and sustainable development",
                "icon": "🌱", "color": "#006400", "category": "वातावरण"
            },
            {
                "name": "समाज र समुदाय", "name_en": "Society & Community",
                "description": "नेपाली समाज र विविध समुदायहरू",
                "description_en": "Nepali society and diverse communities",
                "icon": "👥", "color": "#8A2BE2", "category": "समाज"
            }
        ]
    
    def generate_real_content_sets(self, theme_name: str) -> List[Dict]:
        """Generate 2 real content sets with actual content for each theme."""

        # Real content data for each theme
        theme_content = {
            "भूगोल र वातावरण": [
                {
                    "title": "हिमाल र नदीहरू",
                    "script": """नेपाल हिमालयको काखमा बसेको एक सुन्दर देश हो। यहाँ संसारकै सबैभन्दा अग्लो हिमाल सगरमाथा छ जसको उचाइ ८,८४९ मिटर छ। नेपालमा तीन मुख्य भौगोलिक क्षेत्रहरू छन् - हिमाल, पहाड र तराई। हिमाली क्षेत्र समुद्री सतहबाट ४,८७७ मिटरभन्दा माथि अवस्थित छ। यहाँका मुख्य नदीहरू कोशी, गण्डकी र कर्णाली हुन्। नेपालको सबैभन्दा लामो नदी कर्णाली हो जसको लम्बाइ ५०७ किलोमिटर छ। नेपालको सबैभन्दा ठूलो ताल रारा ताल हो जुन मुगु जिल्लामा अवस्थित छ र यसको क्षेत्रफल १०.८ वर्ग किलोमिटर छ।""",
                    "remark": "Mountains and rivers of Nepal"
                },
                {
                    "title": "राष्ट्रिय निकुञ्ज र जलवायु",
                    "script": """नेपालमा धेरै राष्ट्रिय निकुञ्जहरू छन् जसले वन्यजन्तु र प्राकृतिक सम्पदाको संरक्षण गर्छन्। चितवन राष्ट्रिय निकुञ्ज नेपालको पहिलो राष्ट्रिय निकुञ्ज हो जुन १९७३ मा स्थापना भएको थियो र यसको क्षेत्रफल ९३२ वर्ग किलोमिटर छ। सगरमाथा राष्ट्रिय निकुञ्ज सोलुखुम्बु जिल्लामा अवस्थित छ र यो १९७६ मा स्थापना भएको थियो। नेपालको जलवायु मनसुनी प्रकारको छ र यहाँ मुख्यतः दक्षिण-पश्चिमी मनसुनबाट वर्षा हुन्छ। नेपालको कुल भूभागको ४४.७% भाग वन क्षेत्र छ। नेपालमा वार्षिक औसत वर्षा १,५०० मिलिमिटर हुन्छ।""",
                    "remark": "National parks and climate of Nepal"
                }
            ],
            "नेपाली संस्कृति": [
                {
                    "title": "राष्ट्रिय प्रतीकहरू र पहिचान",
                    "script": """नेपाल एक सांस्कृतिक रूपमा धनी देश हो। यहाँको राष्ट्रिय पशु एक सिङ्गे गैंडा हो जुन चितवन र बर्दिया राष्ट्रिय निकुञ्जमा पाइन्छ। नेपालको राष्ट्रिय फूल गुराँस हो जुन हिमालमा फुल्छ र यसका ३० भन्दा बढी प्रजातिहरू छन्। नेपालको राष्ट्रिय चरा डाँफे हो जुन रंगबिरंगी र सुन्दर छ। नेपाली झण्डा संसारमा एक मात्र त्रिकोणाकार झण्डा हो जसमा रातो र निलो रङ छ। रातो रङले वीरताको प्रतीक गर्छ भने निलो रङले शान्तिको प्रतीक गर्छ। नेपालको राष्ट्रिय गान "सयौं थुंगा फूलका हामी" बाट सुरु हुन्छ जुन अमर सिंह थापाले लेखेका थिए।""",
                    "remark": "National symbols and identity of Nepal"
                },
                {
                    "title": "पारम्परिक पोशाक र रीतिरिवाज",
                    "script": """नेपाली संस्कृतिमा पारम्परिक पोशाकको विशेष महत्व छ। पुरुषहरूले दौरा सुरुवाल र धाका टोपी लगाउँछन्। दौरा सुरुवालमा ८ वटा तार हुन्छन् जसले अष्टांग योगको प्रतीक गर्छ। महिलाहरूले गुन्यु चोलो लगाउँछन् जुन नेवार समुदायको पारम्परिक पोशाक हो। नेपालीहरूले हात जोडेर "नमस्कार" भन्छन् जसको अर्थ "तपाईंलाई सम्मान गर्छु" हो। खुकुरी नेपालको वीरता र साहसको प्रतीक हो र यो गोर्खाली सैनिकहरूको मुख्य हतियार हो। नेपालमा दशैं, तिहार र होली जस्ता मुख्य चाडपर्वहरू मनाइन्छन्। दशैं नेपालको सबैभन्दा ठूलो चाड हो जुन १५ दिनसम्म मनाइन्छ।""",
                    "remark": "Traditional dress and customs of Nepal"
                }
            ]
        }

        # Return real content if available, otherwise generate basic content
        if theme_name in theme_content:
            content_sets = theme_content[theme_name]
        else:
            # Generate basic content for other themes
            content_sets = [
                {
                    "title": f"{theme_name} - मुख्य पक्षहरू",
                    "script": f"""नेपालमा {theme_name} को विशेष महत्व छ। यसले नेपाली समाज र संस्कृतिमा गहिरो प्रभाव पारेको छ। {theme_name} को इतिहास धेरै पुरानो छ र यसका विविध पक्षहरू छन्। आजको युगमा पनि {theme_name} को प्रासंगिकता कायम छ। नेपालका विभिन्न क्षेत्रमा {theme_name} को फरक फरक रूप देख्न सकिन्छ।""",
                    "remark": f"Main aspects of {theme_name}"
                },
                {
                    "title": f"{theme_name} - भविष्यका चुनौतीहरू",
                    "script": f"""आजको युगमा {theme_name} ले नयाँ चुनौतीहरूको सामना गरिरहेको छ। प्रविधिको विकासले {theme_name} मा परिवर्तन ल्याएको छ। भविष्यमा {theme_name} को संरक्षण र विकास आवश्यक छ। यसका लागि सबैको सहयोग चाहिन्छ। नयाँ पुस्ताले {theme_name} को महत्व बुझ्नुपर्छ।""",
                    "remark": f"Future challenges of {theme_name}"
                }
            ]

        # Add questions to each content set
        for content_set in content_sets:
            content_set["questions"] = self.generate_real_questions(theme_name, content_set["title"])

        return content_sets
    
    def generate_real_questions(self, theme_name: str, content_title: str) -> List[Dict]:
        """Generate 5 real questions based on the theme and content."""

        # Real questions for specific themes
        real_questions = {
            ("भूगोल र वातावरण", "हिमाल र नदीहरू"): [
                {
                    "type": "single_choice",
                    "title": "सगरमाथाको उचाइ",
                    "question": {
                        "text": "स्क्रिप्ट अनुसार सगरमाथाको उचाइ कति मिटर छ?",
                        "translated_text": "According to the script, what is the height of Mount Everest in meters?",
                        "options": {"a": "८,८४८ मिटर", "b": "८,८४९ मिटर", "c": "८,८५० मिटर", "d": "८,८४७ मिटर"},
                        "answer_hint": "स्क्रिप्टमा स्पष्ट रूपमा उल्लेख गरिएको छ"
                    },
                    "correct_answer": {"value": "b", "type": "single"},
                    "score": 10,
                    "difficulty": 1
                },
                {
                    "type": "multiple_choice",
                    "title": "नेपालका भौगोलिक क्षेत्रहरू",
                    "question": {
                        "text": "स्क्रिप्ट अनुसार नेपालका तीन मुख्य भौगोलिक क्षेत्रहरू कुन कुन हुन्?",
                        "translated_text": "According to the script, what are the three main geographical regions of Nepal?",
                        "options": {"a": "हिमाल", "b": "पहाड", "c": "तराई", "d": "मरुभूमि"},
                        "answer_hint": "स्क्रिप्टमा तीन क्षेत्रहरू उल्लेख गरिएको छ"
                    },
                    "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"},
                    "score": 15,
                    "difficulty": 2
                },
                {
                    "type": "single_choice",
                    "title": "नेपालको सबैभन्दा लामो नदी",
                    "question": {
                        "text": "स्क्रिप्ट अनुसार नेपालको सबैभन्दा लामो नदी कुन हो र यसको लम्बाइ कति छ?",
                        "translated_text": "According to the script, which is the longest river in Nepal and what is its length?",
                        "options": {"a": "कोशी - ४५० किलोमिटर", "b": "गण्डकी - ४८० किलोमिटर", "c": "कर्णाली - ५०७ किलोमिटर", "d": "महाकाली - ३५० किलोमिटर"},
                        "answer_hint": "स्क्रिप्टमा नदीको नाम र लम्बाइ दुवै उल्लेख छ"
                    },
                    "correct_answer": {"value": "c", "type": "single"},
                    "score": 15,
                    "difficulty": 3
                },
                {
                    "type": "single_choice",
                    "title": "हिमाली क्षेत्रको उचाइ",
                    "question": {
                        "text": "स्क्रिप्ट अनुसार हिमाली क्षेत्र समुद्री सतहबाट कति मिटरभन्दा माथि अवस्थित छ?",
                        "translated_text": "According to the script, at what height above sea level is the Himalayan region located?",
                        "options": {"a": "४,५०० मिटरभन्दा माथि", "b": "४,८७७ मिटरभन्दा माथि", "c": "५,००० मिटरभन्दा माथि", "d": "५,५०० मिटरभन्दा माथि"},
                        "answer_hint": "स्क्रिप्टमा सटीक संख्या दिइएको छ"
                    },
                    "correct_answer": {"value": "b", "type": "single"},
                    "score": 10,
                    "difficulty": 2
                },
                {
                    "type": "single_choice",
                    "title": "रारा तालको विवरण",
                    "question": {
                        "text": "स्क्रिप्ट अनुसार रारा ताल कुन जिल्लामा छ र यसको क्षेत्रफल कति छ?",
                        "translated_text": "According to the script, in which district is Rara Lake located and what is its area?",
                        "options": {"a": "मुगु जिल्ला - १०.८ वर्ग किलोमिटर", "b": "जुम्ला जिल्ला - १२.५ वर्ग किलोमिटर", "c": "डोल्पा जिल्ला - ९.८ वर्ग किलोमिटर", "d": "हुम्ला जिल्ला - ११.२ वर्ग किलोमिटर"},
                        "answer_hint": "स्क्रिप्टमा जिल्ला र क्षेत्रफल दुवै उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 15,
                    "difficulty": 3
                }
            ]
        }

        # Return real questions if available, otherwise generate generic ones
        key = (theme_name, content_title)
        if key in real_questions:
            return real_questions[key]
        else:
            # Generate basic questions for other content
            return [
                {
                    "type": "single_choice",
                    "title": f"{content_title} - मुख्य विशेषता",
                    "question": {
                        "text": f"स्क्रिप्ट अनुसार {content_title} को मुख्य विशेषता के हो?",
                        "translated_text": f"According to the script, what is the main feature of {content_title}?",
                        "options": {"a": "विशेष महत्व", "b": "सामान्य महत्व", "c": "कम महत्व", "d": "कुनै महत्व छैन"},
                        "answer_hint": "स्क्रिप्टमा महत्वको बारेमा उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 10,
                    "difficulty": 1
                },
                {
                    "type": "single_choice",
                    "title": f"{content_title} - समाजमा प्रभाव",
                    "question": {
                        "text": f"स्क्रिप्ट अनुसार {content_title} ले नेपाली समाजमा कस्तो प्रभाव पारेको छ?",
                        "translated_text": f"According to the script, what kind of impact has {content_title} had on Nepali society?",
                        "options": {"a": "गहिरो प्रभाव", "b": "सामान्य प्रभाव", "c": "कम प्रभाव", "d": "कुनै प्रभाव छैन"},
                        "answer_hint": "स्क्रिप्टमा समाजमा प्रभावको बारेमा उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 10,
                    "difficulty": 1
                },
                {
                    "type": "single_choice",
                    "title": f"{content_title} - इतिहास",
                    "question": {
                        "text": f"स्क्रिप्ट अनुसार {content_title} को इतिहास कस्तो छ?",
                        "translated_text": f"According to the script, what is the history of {content_title}?",
                        "options": {"a": "धेरै पुरानो", "b": "मध्यम पुरानो", "c": "नयाँ", "d": "अज्ञात"},
                        "answer_hint": "स्क्रिप्टमा इतिहासको बारेमा उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 10,
                    "difficulty": 1
                },
                {
                    "type": "single_choice",
                    "title": f"{content_title} - प्रासंगिकता",
                    "question": {
                        "text": f"स्क्रिप्ट अनुसार आजको युगमा {content_title} को प्रासंगिकता कस्तो छ?",
                        "translated_text": f"According to the script, what is the relevance of {content_title} in today's era?",
                        "options": {"a": "कायम छ", "b": "घट्दै छ", "c": "बढ्दै छ", "d": "समाप्त भएको छ"},
                        "answer_hint": "स्क्रिप्टमा आजको प्रासंगिकताको बारेमा उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 10,
                    "difficulty": 2
                },
                {
                    "type": "single_choice",
                    "title": f"{content_title} - भविष्यको आवश्यकता",
                    "question": {
                        "text": f"स्क्रिप्ट अनुसार {content_title} को भविष्यका लागि के आवश्यक छ?",
                        "translated_text": f"According to the script, what is needed for the future of {content_title}?",
                        "options": {"a": "संरक्षण र विकास", "b": "बेवास्ता", "c": "परिवर्तन मात्र", "d": "अध्ययन मात्र"},
                        "answer_hint": "स्क्रिप्टमा भविष्यको आवश्यकताको बारेमा उल्लेख छ"
                    },
                    "correct_answer": {"value": "a", "type": "single"},
                    "score": 10,
                    "difficulty": 2
                }
            ]

    def create_theme_structure(self, theme_template: Dict) -> Dict[str, Any]:
        """Create a complete theme with 2 content sets and 10 task items."""
        theme_id = ObjectId()

        theme = {
            "_id": theme_id,
            "name": theme_template["name"],
            "name_en": theme_template["name_en"],
            "description": theme_template["description"],
            "description_en": theme_template["description_en"],
            "icon": theme_template["icon"],
            "color": theme_template["color"],
            "category": theme_template["category"],
            "is_active": True,
            "created_at": self.current_time,
            "updated_at": self.current_time
        }

        content_sets_data = self.generate_real_content_sets(theme_template['name'])
        content_sets = []
        all_task_items = []

        # Create 2 content sets per theme
        for set_data in content_sets_data:
            content_set_id = ObjectId()

            content_set = {
                "_id": content_set_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "title": set_data["title"],
                "input_type": "text",
                "input_content": {
                    "script": set_data["script"]
                },
                "tasks": [],  # Will be filled with task IDs
                "stories": [],
                "total_tasks": 5,
                "total_stories": 0,
                "text_tasks_ready": 5,
                "media_tasks_pending": 0,
                "attempted_tasks": 0,
                "total_verified": 0,
                "status": "pending",
                "total_score": 50,
                "scored": 0,
                "attempts_count": 0,
                "created_at": self.current_time,
                "theme_id": theme_id,
                "updated_at": self.current_time,
                "completed_at": None,
                "remark": set_data["remark"],
                "submitted_at": None
            }

            # Create 5 task items per content set
            task_items = []
            for question_data in set_data["questions"]:
                task_item = {
                    "_id": ObjectId(),
                    "task_set_id": content_set_id,
                    "user_id": USER_ID,
                    "session_id": str(uuid.uuid4()),
                    "type": question_data["type"],
                    "title": question_data["title"],
                    "question": {
                        "text": question_data["question"]["text"],
                        "translated_text": question_data["question"]["translated_text"],
                        "options": question_data["question"]["options"],
                        "answer_hint": question_data["question"]["answer_hint"],
                        "metadata": {}
                    },
                    "correct_answer": question_data["correct_answer"],
                    "user_answer": None,
                    "status": "pending",
                    "result": None,
                    "remark": None,
                    "total_score": question_data.get("score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": question_data.get("difficulty", 1),
                    "metadata": {"theme_id": str(theme_id)},
                    "created_at": self.current_time,
                    "updated_at": self.current_time,
                    "answered_at": None,
                    "is_attempted": False,
                    "submitted_by": None,
                    "test_results": None,
                    "test_status": None,
                    "verification_notes": None,
                    "verification_status": "pending",
                    "verified_at": None,
                    "verified_by": None
                }
                task_items.append(task_item)

            # Add task IDs to content set
            content_set["tasks"] = [str(task["_id"]) for task in task_items]
            content_sets.append(content_set)
            all_task_items.extend(task_items)

        return {
            "theme": theme,
            "content_sets": content_sets,
            "task_items": all_task_items
        }

async def main():
    """Generate and insert all 20 themes into database."""
    generator = ComprehensiveThemeGenerator()
    theme_templates = generator.get_theme_templates()

    print(f"🚀 Starting generation and insertion of {len(theme_templates)} themes...")
    print(f"Each theme will have 2 content sets with 5 questions each (10 total per theme)")
    print(f"Total expected: {len(theme_templates)} themes, {len(theme_templates)*2} content sets, {len(theme_templates)*10} task items")
    print("=" * 80)

    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]

    try:
        total_themes = 0
        total_content_sets = 0
        total_task_items = 0

        for i, template in enumerate(theme_templates, 1):
            print(f"📝 Creating Theme {i}: {template['name']}")

            # Create complete theme structure
            result = generator.create_theme_structure(template)

            # Insert theme
            await db.themes.insert_one(result["theme"])
            total_themes += 1

            # Insert content sets
            await db.curated_content_set.insert_many(result["content_sets"])
            total_content_sets += len(result["content_sets"])

            # Insert task items
            await db.curated_content_items.insert_many(result["task_items"])
            total_task_items += len(result["task_items"])

            print(f"   ✓ Inserted theme into database")
            print(f"   ✓ Inserted {len(result['content_sets'])} content sets")
            print(f"   ✓ Inserted {len(result['task_items'])} task items")

        print("=" * 80)
        print("✅ All themes inserted successfully!")
        print("\n📊 Final Database Summary:")
        print(f"   • Total themes inserted: {total_themes}")
        print(f"   • Total content sets inserted: {total_content_sets}")
        print(f"   • Total task items inserted: {total_task_items}")

    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(main())
