#!/usr/bin/env python3
"""
Theme 11: Economy & Business (अर्थतन्त्र र व्यापार)
- 2 content sets with 10 task items total (5 per set)
- Real-world questions with proper difficulty levels
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_economy_business_theme() -> Dict[str, Any]:
    """Create Economy & Business theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "अर्थतन्त्र र व्यापार",
        "name_en": "Economy & Business",
        "description": "नेपालको अर्थतन्त्र र व्यापारिक गतिविधिहरू",
        "description_en": "Nepal's economy and business activities",
        "icon": "💰",
        "color": "#FFD700",
        "category": "अर्थतन्त्र",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Economic Sectors
    content_set_1_id = ObjectId()
    script_1 = """नेपालको अर्थतन्त्र कृषि, उद्योग र सेवा क्षेत्रमा आधारित छ। कृषि क्षेत्रले सबैभन्दा धेरै मानिसहरूलाई रोजगारी दिन्छ। चामल, मकै, गहुँ मुख्य बालीहरू हुन्। पर्यटन उद्योग नेपालको महत्वपूर्ण आम्दानीको स्रोत हो। रेमिट्यान्स वैदेशिक रोजगारीबाट आउने पैसा हो। नेपाली रुपैयाँ नेपालको मुद्रा हो। नेपाल राष्ट्र बैंकले मौद्रिक नीति बनाउँछ। ऊर्जा, जलविद्युत र खानी क्षेत्रमा सम्भावना छ।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "आर्थिक क्षेत्रहरू",
        "input_type": "text",
        "input_content": {"script": script_1},
        "theme_id": theme_id,
        "tasks": [],
        "stories": [],
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Task items for Content Set 1
    task_1_1_id = ObjectId()
    task_1_1 = {
        "_id": task_1_1_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "नेपालको अर्थतन्त्र कुन कुन क्षेत्रमा आधारित छ?",
            "translated_text": "On which sectors is Nepal's economy based?",
            "options": {
                "a": "कृषि, उद्योग, सेवा",
                "b": "कृषि, खेलकुद, कला",
                "c": "उद्योग, शिक्षा, स्वास्थ्य",
                "d": "सेवा, मनोरञ्जन, पर्यटन"
            },
            "answer_hint": "तीन मुख्य आर्थिक क्षेत्र",
            "difficulty": 1,
            "title": "आर्थिक आधार"
        },
        "correct_answer": {"value": "a", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_2_id = ObjectId()
    task_1_2 = {
        "_id": task_1_2_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "रेमिट्यान्स के हो?",
            "translated_text": "What is remittance?",
            "options": {
                "a": "स्थानीय आम्दानी",
                "b": "वैदेशिक रोजगारीबाट आउने पैसा",
                "c": "सरकारी सहायता",
                "d": "व्यापारिक नाफा"
            },
            "answer_hint": "विदेशबाट आउने पैसा",
            "difficulty": 2,
            "title": "रेमिट्यान्सको अर्थ"
        },
        "type": "single_choice",
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_3_id = ObjectId()
    task_1_3 = {
        "_id": task_1_3_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "नेपालको मुद्रा के हो?",
            "translated_text": "What is the currency of Nepal?",
            "options": {
                "a": "डलर",
                "b": "रुपैयाँ",
                "c": "युरो",
                "d": "युआन"
            },
            "answer_hint": "नेपाली मुद्रा",
            "difficulty": 1,
            "title": "राष्ट्रिय मुद्रा"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_4_id = ObjectId()
    task_1_4 = {
        "_id": task_1_4_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "कुन क्षेत्रले सबैभन्दा धेरै रोजगारी दिन्छ?",
            "translated_text": "Which sector provides the most employment?",
            "options": {
                "a": "उद्योग",
                "b": "कृषि",
                "c": "सेवा",
                "d": "पर्यटन"
            },
            "answer_hint": "खेतीपाती क्षेत्र",
            "difficulty": 2,
            "title": "रोजगारी दाता क्षेत्र"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_5_id = ObjectId()
    task_1_5 = {
        "_id": task_1_5_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "नेपाल राष्ट्र बैंकले के बनाउँछ?",
            "translated_text": "What does Nepal Rastra Bank make?",
            "options": {
                "a": "शिक्षा नीति",
                "b": "मौद्रिक नीति",
                "c": "स्वास्थ्य नीति",
                "d": "कृषि नीति"
            },
            "answer_hint": "पैसा सम्बन्धी नीति",
            "difficulty": 2,
            "title": "केन्द्रीय बैंकको काम"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }
    
    content_set_1["tasks"] = [task_1_1_id, task_1_2_id, task_1_3_id, task_1_4_id, task_1_5_id]

    # Content Set 2: Trade and Commerce
    content_set_2_id = ObjectId()
    script_2 = """नेपालले भारत र चीनसँग मुख्य व्यापार गर्छ। निर्यातमा कार्पेट, पश्मिना, चिया प्रमुख छन्। आयातमा पेट्रोलियम, मेसिन, इलेक्ट्रोनिक्स छन्। व्यापार घाटा नेपालको समस्या हो। सीमा नाकाहरूमा भन्सार छ। उद्योग वाणिज्य महासंघ व्यापारीहरूको संस्था हो। बजार अर्थतन्त्र नेपालमा अपनाइएको छ। निजीकरण र उदारीकरण नीति लागू छ। विदेशी लगानी प्रोत्साहन गरिएको छ।"""

    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "व्यापार र वाणिज्य",
        "input_type": "text",
        "input_content": {"script": script_2},
        "theme_id": theme_id,
        "tasks": [],
        "stories": [],
        "created_at": current_time,
        "updated_at": current_time
    }

    # Task items for Content Set 2 (simplified for brevity)
    task_2_1_id = ObjectId()
    task_2_1 = {
        "_id": task_2_1_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "नेपालले कुन कुन देशसँग मुख्य व्यापार गर्छ?",
            "translated_text": "With which countries does Nepal mainly trade?",
            "options": {"a": "भारत र चीन", "b": "अमेरिका र जापान", "c": "बंगलादेश र पाकिस्तान", "d": "थाइल्यान्ड र मलेसिया"},
            "answer_hint": "छिमेकी दुई ठूला देश",
            "difficulty": 1,
            "title": "मुख्य व्यापारिक साझेदार"
        },
        "correct_answer": {"value": "a", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_2_id = ObjectId()
    task_2_2 = {
        "_id": task_2_2_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "नेपालका मुख्य निर्यात वस्तुहरू के के हुन्?",
            "translated_text": "What are Nepal's main export items?",
            "options": {"a": "कार्पेट, पश्मिना, चिया", "b": "चामल, दाल, तेल", "c": "कार, मोटर, साइकल", "d": "कम्प्युटर, मोबाइल, टिभी"},
            "answer_hint": "हस्तकला र कृषि उत्पादन",
            "difficulty": 2,
            "title": "निर्यात वस्तुहरू"
        },
        "correct_answer": {"value": "a", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_3_id = ObjectId()
    task_2_3 = {
        "_id": task_2_3_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "व्यापार घाटा के हो?",
            "translated_text": "What is trade deficit?",
            "options": {"a": "निर्यात बढी हुनु", "b": "आयात बढी हुनु", "c": "निर्यात र आयात बराबर हुनु", "d": "व्यापार नहुनु"},
            "answer_hint": "आयात निर्यातभन्दा बढी",
            "difficulty": 3,
            "title": "व्यापार घाटाको अर्थ"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_4_id = ObjectId()
    task_2_4 = {
        "_id": task_2_4_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "उद्योग वाणिज्य महासंघ के हो?",
            "translated_text": "What is the Federation of Nepalese Chambers of Commerce and Industry?",
            "options": {"a": "सरकारी संस्था", "b": "व्यापारीहरूको संस्था", "c": "शिक्षा संस्था", "d": "स्वास्थ्य संस्था"},
            "answer_hint": "व्यापारी र उद्योगीहरूको संगठन",
            "difficulty": 2,
            "title": "व्यापारिक संस्था"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_5_id = ObjectId()
    task_2_5 = {
        "_id": task_2_5_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "type": "single_choice",
        "question": {
            "text": "नेपालमा कस्तो अर्थतन्त्र अपनाइएको छ?",
            "translated_text": "What type of economy has been adopted in Nepal?",
            "options": {"a": "समाजवादी अर्थतन्त्र", "b": "बजार अर्थतन्त्र", "c": "मिश्रित अर्थतन्त्र", "d": "बन्द अर्थतन्त्र"},
            "answer_hint": "खुला प्रतिस्पर्धामा आधारित",
            "difficulty": 2,
            "title": "आर्थिक प्रणाली"
        },
        "correct_answer": {"value": "b", "type": "single_choice"},
        "created_at": current_time,
        "updated_at": current_time
    }

    content_set_2["tasks"] = [task_2_1_id, task_2_2_id, task_2_3_id, task_2_4_id, task_2_5_id]

    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "tasks": [task_1_1, task_1_2, task_1_3, task_1_4, task_1_5,
                 task_2_1, task_2_2, task_2_3, task_2_4, task_2_5]
    }

async def insert_economy_business_data():
    """Insert Economy & Business theme data into MongoDB."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]

    try:
        # Create the theme data
        theme_data = create_economy_business_theme()

        # Insert theme
        await db.themes.insert_one(theme_data["theme"])
        print(f"✅ Inserted theme: {theme_data['theme']['name']}")

        # Insert content sets
        await db.curated_content_sets.insert_many(theme_data["content_sets"])
        print(f"✅ Inserted {len(theme_data['content_sets'])} content sets")

        # Insert tasks
        await db.tasks.insert_many(theme_data["tasks"])
        print(f"✅ Inserted {len(theme_data['tasks'])} tasks")

        print(f"🎉 Successfully created Economy & Business theme with {len(theme_data['content_sets'])} content sets and {len(theme_data['tasks'])} tasks!")

    except Exception as e:
        print(f"❌ Error inserting data: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(insert_economy_business_data())
