#!/usr/bin/env python3
"""
Master script to generate all 20 themes with 2 content sets each and 10 task items per theme.
Each theme has real-world questions with proper difficulty levels.
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any, List
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

class ThemeGenerator:
    def __init__(self):
        self.current_time = datetime.now(timezone.utc)
    
    def create_theme_structure(self, theme_data: Dict) -> Dict[str, Any]:
        """Create a complete theme with 2 content sets and 10 task items."""
        theme_id = ObjectId()
        
        theme = {
            "_id": theme_id,
            "name": theme_data["name"],
            "name_en": theme_data["name_en"],
            "description": theme_data["description"],
            "description_en": theme_data["description_en"],
            "icon": theme_data["icon"],
            "color": theme_data["color"],
            "category": theme_data["category"],
            "is_active": True,
            "created_at": self.current_time,
            "updated_at": self.current_time
        }
        
        content_sets = []
        all_task_items = []
        
        # Create 2 content sets per theme
        for i, set_data in enumerate(theme_data["content_sets"]):
            content_set_id = ObjectId()
            
            content_set = {
                "_id": content_set_id,
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "title": set_data["title"],
                "input_type": "text",
                "input_content": {
                    "script": set_data["script"]
                },
                "tasks": [],  # Will be filled with task IDs
                "stories": [],
                "total_tasks": 5,
                "total_stories": 0,
                "text_tasks_ready": 5,
                "media_tasks_pending": 0,
                "attempted_tasks": 0,
                "total_verified": 0,
                "status": "pending",
                "total_score": 50,
                "scored": 0,
                "attempts_count": 0,
                "created_at": self.current_time,
                "theme_id": theme_id,
                "updated_at": self.current_time,
                "completed_at": None,
                "remark": set_data["remark"],
                "submitted_at": None
            }
            
            # Create 5 task items per content set
            task_items = []
            for j, question_data in enumerate(set_data["questions"]):
                task_item = {
                    "_id": ObjectId(),
                    "task_set_id": content_set_id,
                    "user_id": USER_ID,
                    "session_id": str(uuid.uuid4()),
                    "type": question_data["type"],
                    "title": question_data["title"],
                    "question": {
                        "text": question_data["question"]["text"],
                        "translated_text": question_data["question"]["translated_text"],
                        "options": question_data["question"]["options"],
                        "answer_hint": question_data["question"]["answer_hint"],
                        "metadata": {}
                    },
                    "correct_answer": question_data["correct_answer"],
                    "user_answer": None,
                    "status": "pending",
                    "result": None,
                    "remark": None,
                    "total_score": question_data.get("score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": question_data.get("difficulty", 1),
                    "metadata": {"theme_id": str(theme_id)},
                    "created_at": self.current_time,
                    "updated_at": self.current_time,
                    "answered_at": None,
                    "is_attempted": False,
                    "submitted_by": None,
                    "test_results": None,
                    "test_status": None,
                    "verification_notes": None,
                    "verification_status": "pending",
                    "verified_at": None,
                    "verified_by": None
                }
                task_items.append(task_item)
            
            # Add task IDs to content set
            content_set["tasks"] = [str(task["_id"]) for task in task_items]
            content_sets.append(content_set)
            all_task_items.extend(task_items)
        
        return {
            "theme": theme,
            "content_sets": content_sets,
            "task_items": all_task_items
        }

def get_all_theme_data() -> List[Dict]:
    """Return data for all 20 themes."""
    themes_data = [
        {
            "name": "भूगोल र वातावरण",
            "name_en": "Geography & Environment",
            "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
            "description_en": "Nepal's geographical structure and environmental diversity",
            "icon": "🏔️",
            "color": "#2E8B57",
            "category": "भूगोल",
            "content_sets": [
                {
                    "title": "हिमाल र नदीहरू",
                    "script": """नेपाल हिमालयको काखमा बसेको एक सुन्दर देश हो। यहाँ संसारकै सबैभन्दा अग्लो हिमाल सगरमाथा छ जसको उचाइ ८,८४९ मिटर छ। नेपालमा तीन मुख्य भौगोलिक क्षेत्रहरू छन् - हिमाल, पहाड र तराई। यहाँका मुख्य नदीहरू कोशी, गण्डकी र कर्णाली हुन्। नेपालको सबैभन्दा लामो नदी कर्णाली हो। नेपालको सबैभन्दा ठूलो ताल रारा ताल हो जुन मुगु जिल्लामा अवस्थित छ।""",
                    "remark": "Mountains and rivers of Nepal",
                    "questions": [
                        {
                            "type": "single_choice",
                            "title": "सगरमाथाको उचाइ",
                            "question": {
                                "text": "स्क्रिप्ट अनुसार सगरमाथाको उचाइ कति मिटर छ?",
                                "translated_text": "According to the script, what is the height of Mount Everest in meters?",
                                "options": {
                                    "a": "८,८४८ मिटर",
                                    "b": "८,८४९ मिटर",
                                    "c": "८,८५० मिटर",
                                    "d": "८,८४७ मिटर"
                                },
                                "answer_hint": "स्क्रिप्टमा स्पष्ट रूपमा उल्लेख गरिएको छ"
                            },
                            "correct_answer": {"value": "b", "type": "single"},
                            "score": 10,
                            "difficulty": 1
                        }
                    ]
                }
            ]
        }
    ]
    
    return themes_data

async def main():
    """Generate and insert all themes into database."""
    generator = ThemeGenerator()
    themes_data = get_all_theme_data()
    
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]
    
    try:
        total_themes = 0
        total_content_sets = 0
        total_task_items = 0
        
        for theme_data in themes_data:
            result = generator.create_theme_structure(theme_data)
            
            # Insert theme
            await db.themes.insert_one(result["theme"])
            total_themes += 1
            
            # Insert content sets
            await db.curated_content_set.insert_many(result["content_sets"])
            total_content_sets += len(result["content_sets"])
            
            # Insert task items
            await db.curated_content_items.insert_many(result["task_items"])
            total_task_items += len(result["task_items"])
            
            print(f"✓ Created theme: {result['theme']['name']}")
        
        print(f"\n📊 Summary:")
        print(f"Total themes created: {total_themes}")
        print(f"Total content sets created: {total_content_sets}")
        print(f"Total task items created: {total_task_items}")
        
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(main())
