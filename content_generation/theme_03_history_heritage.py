#!/usr/bin/env python3
"""
Theme 3: History & Heritage
- 2 content sets with 10 task items each (5 per set)
- Real-world questions with proper difficulty levels
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_history_heritage_theme() -> Dict[str, Any]:
    """Create History & Heritage theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "इतिहास र विरासत",
        "name_en": "History & Heritage",
        "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक विरासत",
        "description_en": "Nepal's historical events and cultural heritage",
        "icon": "📜",
        "color": "#8B4513",
        "category": "इतिहास",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Ancient Dynasties
    content_set_1_id = ObjectId()
    script_1 = """नेपालको इतिहास धेरै पुरानो छ। लिच्छवि काल नेपालको स्वर्ण युग मानिन्छ जुन ४०० देखि ७५० ईस्वी सम्म चलेको थियो। यस समयमा मन्दिर र मूर्तिकलाको विकास भएको थियो। त्यसपछि मल्ल काल आयो जुन १२०० देखि १७६९ ईस्वी सम्म चलेको थियो। मल्ल कालमा काठमाडौं, पाटन र भक्तपुरमा तीन राज्यहरू थिए। यस समयमा न्यूवार कला र संस्कृतिको विकास भएको थियो। राजा जयस्थिति मल्ल एक प्रसिद्ध मल्ल राजा थिए जसले जात प्रथाको व्यवस्था गरेका थिए। मल्ल कालमा पगोडा शैलीका मन्दिरहरू निर्माण भएका थिए।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "प्राचीन राजवंशहरू",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "tasks": [],
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Ancient dynasties of Nepal",
        "submitted_at": None
    }
    
    # Content Set 2: Modern History
    content_set_2_id = ObjectId()
    script_2 = """१७६९ मा पृथ्वीनारायण शाहले काठमाडौं उपत्यका एकीकरण गरेर आधुनिक नेपालको जग गर्नुभयो। उनलाई नेपालको एकीकरणकर्ता भनिन्छ। त्यसपछि राणा शासन सुरु भयो जुन १०४ वर्षसम्म चल्यो। जंगबहादुर राणा पहिलो राणा प्रधानमन्त्री थिए। राणा कालमा नेपाल बाहिरी संसारबाट बन्द थियो। २००७ सालमा राणा शासनको अन्त्य भयो र राजा त्रिभुवनले लोकतन्त्रको सुरुवात गर्नुभयो। २०४६ सालमा जनआन्दोलनपछि बहुदलीय लोकतन्त्र स्थापना भयो।"""
    
    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "आधुनिक इतिहास",
        "input_type": "text",
        "input_content": {
            "script": script_2
        },
        "tasks": [],
        "stories": [],
        "total_tasks": 5,
        "total_stories": 0,
        "text_tasks_ready": 5,
        "media_tasks_pending": 0,
        "attempted_tasks": 0,
        "total_verified": 0,
        "status": "pending",
        "total_score": 50,
        "scored": 0,
        "attempts_count": 0,
        "created_at": current_time,
        "theme_id": theme_id,
        "updated_at": current_time,
        "completed_at": None,
        "remark": "Modern history of Nepal",
        "submitted_at": None
    }
    
    # Task items for Content Set 1 (5 items with varying difficulty)
    task_items_1 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको स्वर्ण युग",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको स्वर्ण युग कुन काल मानिन्छ?",
                "translated_text": "According to the script, which period is considered the golden age of Nepal?",
                "options": {
                    "a": "मल्ल काल",
                    "b": "लिच्छवि काल",
                    "c": "शाह काल",
                    "d": "राणा काल"
                },
                "answer_hint": "स्क्रिप्टमा स्वर्ण युग भनिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_1_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "लिच्छवि काल अवधि",
            "question": {
                "text": "स्क्रिप्ट अनुसार लिच्छवि काल कुन समयदेखि कुन समयसम्म चलेको थियो?",
                "translated_text": "According to the script, from when to when did the Licchavi period last?",
                "options": {
                    "a": "३०० देखि ६५० ईस्वी",
                    "b": "४०० देखि ७५० ईस्वी",
                    "c": "५०० देखि ८०० ईस्वी",
                    "d": "६०० देखि ९०० ईस्वी"
                },
                "answer_hint": "स्क्रिप्टमा सटीक अवधि उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Task items for Content Set 2 (5 items with varying difficulty)
    task_items_2 = [
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "नेपालको एकीकरणकर्ता",
            "question": {
                "text": "स्क्रिप्ट अनुसार नेपालको एकीकरणकर्ता को हुन्?",
                "translated_text": "According to the script, who is the unifier of Nepal?",
                "options": {
                    "a": "पृथ्वीनारायण शाह",
                    "b": "जंगबहादुर राणा",
                    "c": "राजा त्रिभुवन",
                    "d": "जयस्थिति मल्ल"
                },
                "answer_hint": "स्क्रिप्टमा एकीकरणकर्ता भनिएको छ",
                "metadata": {}
            },
            "correct_answer": {"value": "a", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 1,
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        },
        {
            "_id": ObjectId(),
            "task_set_id": content_set_2_id,
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "type": "single_choice",
            "title": "राणा शासनको अवधि",
            "question": {
                "text": "स्क्रिप्ट अनुसार राणा शासन कति वर्षसम्म चलेको थियो?",
                "translated_text": "According to the script, for how many years did the Rana rule last?",
                "options": {
                    "a": "१०० वर्ष",
                    "b": "१०४ वर्ष",
                    "c": "११० वर्ष",
                    "d": "१२० वर्ष"
                },
                "answer_hint": "स्क्रिप्टमा सटीक वर्ष उल्लेख छ",
                "metadata": {}
            },
            "correct_answer": {"value": "b", "type": "single"},
            "user_answer": None,
            "status": "pending",
            "result": None,
            "remark": None,
            "total_score": 15,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": 2,
            "metadata": {"theme_id": str(theme_id)},
            "created_at": current_time,
            "updated_at": current_time,
            "answered_at": None,
            "is_attempted": False,
            "submitted_by": None,
            "test_results": None,
            "test_status": None,
            "verification_notes": None,
            "verification_status": "pending",
            "verified_at": None,
            "verified_by": None
        }
    ]

    # Add task IDs to content sets
    content_set_1["tasks"] = [str(task["_id"]) for task in task_items_1]
    content_set_2["tasks"] = [str(task["_id"]) for task in task_items_2]
    
    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "task_items": task_items_1 + task_items_2
    }

if __name__ == "__main__":
    data = create_history_heritage_theme()
    print(f"Created theme: {data['theme']['name']}")
    print(f"Content sets: {len(data['content_sets'])}")
    print(f"Task items: {len(data['task_items'])}")
