#!/usr/bin/env python3
"""
Comprehensive verification of all 20 themes in the database.
Verifies the complete structure: themes -> content_sets -> tasks
"""

import asyncio
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"

async def verify_database_structure():
    """Perform thorough verification of database structure."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]
    
    try:
        print("🔍 Starting comprehensive database verification...")
        print("=" * 60)
        
        # Get all themes
        themes = await db.themes.find({}).sort("_id", 1).to_list(None)
        print(f"📊 Found {len(themes)} themes in database")
        
        if len(themes) != 20:
            print(f"❌ ERROR: Expected 20 themes, found {len(themes)}")
            return False
        
        total_content_sets = 0
        total_tasks = 0
        verification_errors = []
        
        for i, theme in enumerate(themes, 1):
            theme_id = theme["_id"]
            theme_name = theme["name"]
            theme_name_en = theme["name_en"]
            
            print(f"\n🎯 Theme {i}: {theme_name} ({theme_name_en})")
            print(f"   Theme ID: {theme_id}")
            
            # Get content sets for this theme
            content_sets = await db.curated_content_sets.find({"theme_id": theme_id}).to_list(None)
            content_sets_count = len(content_sets)
            total_content_sets += content_sets_count
            
            print(f"   📁 Content Sets: {content_sets_count}")
            
            if content_sets_count != 2:
                error_msg = f"Theme {i} ({theme_name}): Expected 2 content sets, found {content_sets_count}"
                verification_errors.append(error_msg)
                print(f"   ❌ {error_msg}")
            
            theme_tasks_count = 0
            
            for j, content_set in enumerate(content_sets, 1):
                content_set_id = content_set["_id"]
                content_set_title = content_set.get("title", "No title")
                
                print(f"     📋 Content Set {j}: {content_set_title}")
                print(f"        Content Set ID: {content_set_id}")
                
                # Get tasks for this content set
                tasks = await db.tasks.find({"task_set_id": content_set_id}).to_list(None)
                tasks_count = len(tasks)
                theme_tasks_count += tasks_count
                total_tasks += tasks_count
                
                print(f"        📝 Tasks: {tasks_count}")
                
                if tasks_count != 5:
                    error_msg = f"Theme {i} Content Set {j}: Expected 5 tasks, found {tasks_count}"
                    verification_errors.append(error_msg)
                    print(f"        ❌ {error_msg}")
                
                # Verify task structure
                for k, task in enumerate(tasks, 1):
                    task_id = task["_id"]
                    
                    # Check required fields
                    required_fields = ["question", "correct_answer"]
                    missing_fields = [field for field in required_fields if field not in task]
                    
                    if missing_fields:
                        error_msg = f"Theme {i} Content Set {j} Task {k}: Missing fields: {missing_fields}"
                        verification_errors.append(error_msg)
                        print(f"          ❌ {error_msg}")
                    
                    # Check correct_answer structure
                    if "correct_answer" in task:
                        correct_answer = task["correct_answer"]
                        if "type" not in correct_answer:
                            error_msg = f"Theme {i} Content Set {j} Task {k}: Missing 'type' in correct_answer"
                            verification_errors.append(error_msg)
                            print(f"          ❌ {error_msg}")
                        elif correct_answer["type"] not in ["single_choice", "multiple_choice"]:
                            error_msg = f"Theme {i} Content Set {j} Task {k}: Invalid type '{correct_answer['type']}'"
                            verification_errors.append(error_msg)
                            print(f"          ❌ {error_msg}")
                
                # Verify content set has correct task references
                content_set_task_refs = content_set.get("tasks", [])
                if len(content_set_task_refs) != tasks_count:
                    error_msg = f"Theme {i} Content Set {j}: Task references mismatch. Content set has {len(content_set_task_refs)} refs, found {tasks_count} actual tasks"
                    verification_errors.append(error_msg)
                    print(f"        ❌ {error_msg}")
            
            print(f"   📊 Total tasks for theme: {theme_tasks_count}")
            
            if theme_tasks_count != 10:
                error_msg = f"Theme {i}: Expected 10 total tasks, found {theme_tasks_count}"
                verification_errors.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        print("\n" + "=" * 60)
        print("📊 VERIFICATION SUMMARY")
        print("=" * 60)
        print(f"Themes: {len(themes)}/20 ✅" if len(themes) == 20 else f"Themes: {len(themes)}/20 ❌")
        print(f"Content Sets: {total_content_sets}/40 ✅" if total_content_sets == 40 else f"Content Sets: {total_content_sets}/40 ❌")
        print(f"Tasks: {total_tasks}/200 ✅" if total_tasks == 200 else f"Tasks: {total_tasks}/200 ❌")
        
        if verification_errors:
            print(f"\n❌ VERIFICATION FAILED - {len(verification_errors)} errors found:")
            for error in verification_errors:
                print(f"   • {error}")
            return False
        else:
            print(f"\n✅ VERIFICATION PASSED - All 20 themes have correct structure!")
            print(f"   • 20 themes ✅")
            print(f"   • 40 content sets (2 per theme) ✅") 
            print(f"   • 200 tasks (5 per content set) ✅")
            print(f"   • All tasks have correct schema ✅")
            return True
            
    except Exception as e:
        print(f"❌ Verification failed with error: {e}")
        return False
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(verify_database_structure())
