#!/usr/bin/env python3
"""
Theme 10: Technology & Innovation (प्रविधि र नवाचार)
- 2 content sets with 10 task items total (5 per set)
- Real-world questions with proper difficulty levels
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "test_nepali_app"
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")

def create_technology_innovation_theme() -> Dict[str, Any]:
    """Create Technology & Innovation theme with 2 content sets and 10 task items."""
    current_time = datetime.now(timezone.utc)
    theme_id = ObjectId()
    
    theme = {
        "_id": theme_id,
        "name": "प्रविधि र नवाचार",
        "name_en": "Technology & Innovation",
        "description": "नेपालमा प्रविधिको विकास र नवाचारहरू",
        "description_en": "Technology development and innovations in Nepal",
        "icon": "💻",
        "color": "#00CED1",
        "category": "प्रविधि",
        "is_active": True,
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Content Set 1: Digital Nepal and IT Development
    content_set_1_id = ObjectId()
    script_1 = """नेपालमा डिजिटल नेपाल कार्यक्रम सुरु भएको छ। इन्टरनेट र मोबाइल फोनको प्रयोग बढ्दै गएको छ। ई-गभर्नेन्स सेवाहरू सुरु भएका छन्। अनलाइन बैंकिङ र डिजिटल पेमेन्ट लोकप्रिय भएको छ। नेपालमा IT कम्पनीहरू बढ्दै छन्। सफ्टवेयर डेभलपमेन्ट र आउटसोर्सिङ बढेको छ। फाइभजी नेटवर्क परीक्षण सुरु भएको छ। साइबर सुरक्षा महत्वपूर्ण विषय बनेको छ। डिजिटल साक्षरता कार्यक्रम चलाइएको छ।"""
    
    content_set_1 = {
        "_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "डिजिटल नेपाल र IT विकास",
        "input_type": "text",
        "input_content": {
            "script": script_1
        },
        "theme_id": theme_id,
        "tasks": [],
        "stories": [],
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Task items for Content Set 1
    task_1_1_id = ObjectId()
    task_1_1 = {
        "_id": task_1_1_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "type": "single_choice",
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "नेपालमा कुन कार्यक्रम सुरु भएको छ?",
            "translated_text": "Which program has been started in Nepal?",
            "options": {
                "a": "डिजिटल नेपाल",
                "b": "स्मार्ट नेपाल",
                "c": "टेक नेपाल",
                "d": "साइबर नेपाल"
            },
            "answer_hint": "डिजिटल युगको कार्यक्रम",
            "difficulty": 1,
            "title": "राष्ट्रिय कार्यक्रम"
        },
        "correct_answer": {
            "value": "a",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_2_id = ObjectId()
    task_1_2 = {
        "_id": task_1_2_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "ई-गभर्नेन्स के हो?",
            "translated_text": "What is e-governance?",
            "options": {
                "a": "इलेक्ट्रोनिक सरकारी सेवा",
                "b": "इन्टरनेट सेवा",
                "c": "मोबाइल सेवा",
                "d": "कम्प्युटर सेवा"
            },
            "answer_hint": "सरकारी सेवाको डिजिटल रूप",
            "difficulty": 2,
            "title": "ई-गभर्नेन्सको अर्थ"
        },
        "correct_answer": {
            "value": "a",
            "type": "single_choice"
        },
        "type": "single_choice",
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_3_id = ObjectId()
    task_1_3 = {
        "_id": task_1_3_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "डिजिटल पेमेन्ट के हो?",
            "translated_text": "What is digital payment?",
            "options": {
                "a": "नगदमा भुक्तानी",
                "b": "अनलाइन भुक्तानी",
                "c": "चेकमा भुक्तानी",
                "d": "सुनमा भुक्तानी"
            },
            "answer_hint": "इन्टरनेट मार्फत पैसा तिर्ने",
            "difficulty": 1,
            "title": "डिजिटल भुक्तानी"
        },
        "correct_answer": {
            "value": "b",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_4_id = ObjectId()
    task_1_4 = {
        "_id": task_1_4_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "साइबर सुरक्षा किन महत्वपूर्ण छ?",
            "translated_text": "Why is cyber security important?",
            "options": {
                "a": "इन्टरनेट सुरक्षाका लागि",
                "b": "कम्प्युटर सुरक्षाका लागि",
                "c": "डाटा सुरक्षाका लागि",
                "d": "माथिका सबै"
            },
            "answer_hint": "डिजिटल सुरक्षाका लागि",
            "difficulty": 2,
            "title": "साइबर सुरक्षाको महत्व"
        },
        "correct_answer": {
            "value": "d",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }
    
    task_1_5_id = ObjectId()
    task_1_5 = {
        "_id": task_1_5_id,
        "task_set_id": content_set_1_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "नेपालमा कुन नेटवर्कको परीक्षण सुरु भएको छ?",
            "translated_text": "Which network testing has started in Nepal?",
            "options": {
                "a": "3G",
                "b": "4G",
                "c": "5G",
                "d": "6G"
            },
            "answer_hint": "सबैभन्दा नयाँ मोबाइल नेटवर्क",
            "difficulty": 2,
            "title": "नयाँ नेटवर्क"
        },
        "correct_answer": {
            "value": "c",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }
    
    # Update content set 1 with task IDs
    content_set_1["tasks"] = [task_1_1_id, task_1_2_id, task_1_3_id, task_1_4_id, task_1_5_id]

    # Content Set 2: Innovation and Startups
    content_set_2_id = ObjectId()
    script_2 = """नेपालमा स्टार्टअप कम्पनीहरू बढ्दै छन्। फिनटेक कम्पनीहरूले डिजिटल भुक्तानी सेवा दिइरहेका छन्। ई-कमर्स प्लेटफर्महरू लोकप्रिय भएका छन्। मोबाइल एप डेभलपमेन्ट बढेको छ। आर्टिफिसियल इन्टेलिजेन्स र मेसिन लर्निङको प्रयोग सुरु भएको छ। ब्लकचेन टेक्नोलोजीमा अनुसन्धान भइरहेको छ। रोबोटिक्स र अटोमेसनमा काम भइरहेको छ। क्लाउड कम्प्युटिङ सेवाहरू बढेका छन्। इन्टरनेट अफ थिङ्स (IoT) को प्रयोग सुरु भएको छ।"""

    content_set_2 = {
        "_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "title": "नवाचार र स्टार्टअपहरू",
        "input_type": "text",
        "input_content": {
            "script": script_2
        },
        "theme_id": theme_id,
        "tasks": [],
        "stories": [],
        "created_at": current_time,
        "updated_at": current_time
    }

    # Task items for Content Set 2
    task_2_1_id = ObjectId()
    task_2_1 = {
        "_id": task_2_1_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "फिनटेक कम्पनीहरूले के सेवा दिन्छन्?",
            "translated_text": "What services do fintech companies provide?",
            "options": {
                "a": "खाना सेवा",
                "b": "डिजिटल भुक्तानी सेवा",
                "c": "यातायात सेवा",
                "d": "शिक्षा सेवा"
            },
            "answer_hint": "वित्तीय प्रविधि सेवा",
            "difficulty": 2,
            "title": "फिनटेक सेवा"
        },
        "correct_answer": {
            "value": "b",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_2_id = ObjectId()
    task_2_2 = {
        "_id": task_2_2_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "AI को पूरा नाम के हो?",
            "translated_text": "What is the full form of AI?",
            "options": {
                "a": "Artificial Intelligence",
                "b": "Automatic Information",
                "c": "Advanced Internet",
                "d": "Application Interface"
            },
            "answer_hint": "कृत्रिम बुद्धिमत्ता",
            "difficulty": 2,
            "title": "AI को अर्थ"
        },
        "correct_answer": {
            "value": "a",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_3_id = ObjectId()
    task_2_3 = {
        "_id": task_2_3_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "IoT को पूरा नाम के हो?",
            "translated_text": "What is the full form of IoT?",
            "options": {
                "a": "Internet of Technology",
                "b": "Internet of Things",
                "c": "Information of Technology",
                "d": "Interface of Things"
            },
            "answer_hint": "वस्तुहरूको इन्टरनेट",
            "difficulty": 3,
            "title": "IoT को अर्थ"
        },
        "correct_answer": {
            "value": "b",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_4_id = ObjectId()
    task_2_4 = {
        "_id": task_2_4_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "क्लाउड कम्प्युटिङ के हो?",
            "translated_text": "What is cloud computing?",
            "options": {
                "a": "आकाशमा कम्प्युटर",
                "b": "इन्टरनेट मार्फत कम्प्युटिङ सेवा",
                "c": "मौसम पूर्वानुमान",
                "d": "हावाई सेवा"
            },
            "answer_hint": "इन्टरनेट आधारित सेवा",
            "difficulty": 2,
            "title": "क्लाउड कम्प्युटिङ"
        },
        "correct_answer": {
            "value": "b",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }

    task_2_5_id = ObjectId()
    task_2_5 = {
        "_id": task_2_5_id,
        "task_set_id": content_set_2_id,
        "user_id": USER_ID,
        "session_id": str(uuid.uuid4()),
        "question": {
            "text": "ब्लकचेन टेक्नोलोजी केमा प्रयोग हुन्छ?",
            "translated_text": "Where is blockchain technology used?",
            "options": {
                "a": "डिजिटल मुद्रामा",
                "b": "डाटा सुरक्षामा",
                "c": "लेनदेन रेकर्डमा",
                "d": "माथिका सबै"
            },
            "answer_hint": "सुरक्षित डिजिटल लेनदेन",
            "difficulty": 3,
            "title": "ब्लकचेनको प्रयोग"
        },
        "correct_answer": {
            "value": "d",
            "type": "single_choice"
        },
        "created_at": current_time,
        "updated_at": current_time
    }

    # Update content set 2 with task IDs
    content_set_2["tasks"] = [task_2_1_id, task_2_2_id, task_2_3_id, task_2_4_id, task_2_5_id]

    return {
        "theme": theme,
        "content_sets": [content_set_1, content_set_2],
        "tasks": [task_1_1, task_1_2, task_1_3, task_1_4, task_1_5,
                 task_2_1, task_2_2, task_2_3, task_2_4, task_2_5]
    }

async def insert_technology_innovation_data():
    """Insert Technology & Innovation theme data into MongoDB."""
    client = AsyncMongoClient(DB_URL)
    db = client[DB_NAME]

    try:
        # Create the theme data
        theme_data = create_technology_innovation_theme()

        # Insert theme
        await db.themes.insert_one(theme_data["theme"])
        print(f"✅ Inserted theme: {theme_data['theme']['name']}")

        # Insert content sets
        await db.curated_content_sets.insert_many(theme_data["content_sets"])
        print(f"✅ Inserted {len(theme_data['content_sets'])} content sets")

        # Insert tasks
        await db.tasks.insert_many(theme_data["tasks"])
        print(f"✅ Inserted {len(theme_data['tasks'])} tasks")

        print(f"🎉 Successfully created Technology & Innovation theme with {len(theme_data['content_sets'])} content sets and {len(theme_data['tasks'])} tasks!")

    except Exception as e:
        print(f"❌ Error inserting data: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(insert_technology_innovation_data())
